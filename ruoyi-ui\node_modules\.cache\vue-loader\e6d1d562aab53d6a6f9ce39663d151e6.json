{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754295969458}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RIb21lTW9kdWxlLCBnZXRIb21lTW9kdWxlLCBkZWxIb21lTW9kdWxlLCBhZGRIb21lTW9kdWxlLCB1cGRhdGVIb21lTW9kdWxlLCBjaGVja01vZHVsZUNvZGVVbmlxdWUgfSBmcm9tICJAL2FwaS9taW5pYXBwL2hvbWVtb2R1bGUiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJIb21lTW9kdWxlIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdl+ihqOagvOaVsOaNrgogICAgICBob21lTW9kdWxlTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgbW9kdWxlTmFtZTogbnVsbCwKICAgICAgICBtb2R1bGVDb2RlOiBudWxsLAogICAgICAgIGxpbmtUeXBlOiBudWxsLAogICAgICAgIHN0YXR1czogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOaOkuW6j+mYsuaKluWumuaXtuWZqAogICAgICBzb3J0T3JkZXJUaW1lcjogbnVsbCwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgbW9kdWxlTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaooeWdl+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDEsIG1heDogMTAwLCBtZXNzYWdlOiAi5qih5Z2X5ZCN56ew6ZW/5bqm5b+F6aG75LuL5LqOIDEg5ZKMIDEwMCDkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgbW9kdWxlQ29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaooeWdl+S7o+eggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDEsIG1heDogNTAsIG1lc3NhZ2U6ICLmqKHlnZfku6PnoIHplb/luqblv4Xpobvku4vkuo4gMSDlkowgNTAg5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZU1vZHVsZUNvZGUsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBsaW5rVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumTvuaOpeexu+Wei+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIG1vZHVsZVVybDogWwogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVNb2R1bGVVcmwsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBleHRlcm5hbFVybDogWwogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVFeHRlcm5hbFVybCwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHN0YXR1czogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueKtuaAgeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBiZWZvcmVEZXN0cm95KCkgewogICAgLy8g5riF55CG5a6a5pe25ZmoCiAgICBpZiAodGhpcy5zb3J0T3JkZXJUaW1lcikgewogICAgICBjbGVhclRpbWVvdXQodGhpcy5zb3J0T3JkZXJUaW1lcik7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5bCP56iL5bqP6aaW6aG15Yqf6IO95qih5Z2X5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0SG9tZU1vZHVsZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmhvbWVNb2R1bGVMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBtb2R1bGVOYW1lOiBudWxsLAogICAgICAgIG1vZHVsZUljb246IG51bGwsCiAgICAgICAgbW9kdWxlQ29kZTogbnVsbCwKICAgICAgICBtb2R1bGVVcmw6IG51bGwsCiAgICAgICAgbGlua1R5cGU6ICIxIiwKICAgICAgICBleHRlcm5hbFVybDogbnVsbCwKICAgICAgICBzb3J0T3JkZXI6IDAsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgcmVtYXJrOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdlyI7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzCiAgICAgIGdldEhvbWVNb2R1bGUoaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdlyI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlSG9tZU1vZHVsZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZEhvbWVNb2R1bGUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5bCP56iL5bqP6aaW6aG15Yqf6IO95qih5Z2X57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbEhvbWVNb2R1bGUoaWRzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ21pbmlhcHAvaG9tZW1vZHVsZS9leHBvcnQnLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcwogICAgICB9LCBgaG9tZW1vZHVsZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0sCiAgICAvKiog6ZO+5o6l57G75Z6L5pS55Y+Y5aSE55CGICovCiAgICBoYW5kbGVMaW5rVHlwZUNoYW5nZSh2YWx1ZSkgewogICAgICAvLyDmuIXnqbrnm7jlhbPlrZfmrrUKICAgICAgaWYgKHZhbHVlID09PSAnMScpIHsKICAgICAgICB0aGlzLmZvcm0uZXh0ZXJuYWxVcmwgPSBudWxsOwogICAgICB9IGVsc2UgaWYgKHZhbHVlID09PSAnMicpIHsKICAgICAgICB0aGlzLmZvcm0ubW9kdWxlVXJsID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIC8qKiDmjpLluo/lgLzmlLnlj5jlpITnkIYgKi8KICAgIGhhbmRsZVNvcnRPcmRlckNoYW5nZShyb3cpIHsKICAgICAgLy8g6Ziy5oqW5aSE55CG77yM6YG/5YWN6aKR57mB6K+35rGCCiAgICAgIGlmICh0aGlzLnNvcnRPcmRlclRpbWVyKSB7CiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuc29ydE9yZGVyVGltZXIpOwogICAgICB9CiAgICAgIHRoaXMuc29ydE9yZGVyVGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICBjb25zdCB1cGRhdGVEYXRhID0gewogICAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICAgIG1vZHVsZU5hbWU6IHJvdy5tb2R1bGVOYW1lLAogICAgICAgICAgbW9kdWxlSWNvbjogcm93Lm1vZHVsZUljb24sCiAgICAgICAgICBtb2R1bGVDb2RlOiByb3cubW9kdWxlQ29kZSwKICAgICAgICAgIG1vZHVsZVVybDogcm93Lm1vZHVsZVVybCwKICAgICAgICAgIGxpbmtUeXBlOiByb3cubGlua1R5cGUsCiAgICAgICAgICBleHRlcm5hbFVybDogcm93LmV4dGVybmFsVXJsLAogICAgICAgICAgc29ydE9yZGVyOiByb3cuc29ydE9yZGVyLAogICAgICAgICAgc3RhdHVzOiByb3cuc3RhdHVzLAogICAgICAgICAgcmVtYXJrOiByb3cucmVtYXJrCiAgICAgICAgfTsKICAgICAgICB1cGRhdGVIb21lTW9kdWxlKHVwZGF0ZURhdGEpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o6S5bqP5pu05paw5oiQ5YqfIik7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsgLy8g6YeN5paw5Yqg6L295YiX6KGo5Lul5pi+56S65pyA5paw5o6S5bqPCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuaOkuW6j+abtOaWsOWksei0pSIpOwogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7IC8vIOWksei0peaXtuS5n+mHjeaWsOWKoOi9ve+8jOaBouWkjeWOn+Wni+aVsOaNrgogICAgICAgIH0pOwogICAgICB9LCA4MDApOyAvLyA4MDBtc+mYsuaKlgogICAgfSwKICAgIC8qKiDmqKHlnZfku6PnoIHmoKHpqowgKi8KICAgIHZhbGlkYXRlTW9kdWxlQ29kZShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHZhbHVlKSB7CiAgICAgICAgY29uc3QgZGF0YSA9IHsKICAgICAgICAgIGlkOiB0aGlzLmZvcm0uaWQsCiAgICAgICAgICBtb2R1bGVDb2RlOiB2YWx1ZQogICAgICAgIH07CiAgICAgICAgY2hlY2tNb2R1bGVDb2RlVW5pcXVlKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5qih5Z2X5Luj56CB5bey5a2Y5ZyoIikpOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5qCh6aqM5aSx6LSlIikpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5YaF6YOo6ZO+5o6l5qCh6aqMICovCiAgICB2YWxpZGF0ZU1vZHVsZVVybChydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHRoaXMuZm9ybS5saW5rVHlwZSA9PT0gJzEnKSB7CiAgICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlhoXpg6jpk77mjqXkuI3og73kuLrnqboiKSk7CiAgICAgICAgfSBlbHNlIGlmICghdmFsdWUuc3RhcnRzV2l0aCgnLycpKSB7CiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWGhemDqOmTvuaOpeW/hemhu+S7pSAvIOW8gOWktCIpKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDlpJbpg6jpk77mjqXmoKHpqowgKi8KICAgIHZhbGlkYXRlRXh0ZXJuYWxVcmwocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICh0aGlzLmZvcm0ubGlua1R5cGUgPT09ICcyJykgewogICAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5aSW6YOo6ZO+5o6l5LiN6IO95Li656m6IikpOwogICAgICAgIH0gZWxzZSBpZiAoIS9eaHR0cHM/OlwvXC8uKy8udGVzdCh2YWx1ZSkpIHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5aSW6YOo6ZO+5o6l5b+F6aG75LulIGh0dHA6Ly8g5oiWIGh0dHBzOi8vIOW8gOWktCIpKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/config/homemodule", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"模块名称\" prop=\"moduleName\">\n        <el-input\n          v-model=\"queryParams.moduleName\"\n          placeholder=\"请输入模块名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"模块代码\" prop=\"moduleCode\">\n        <el-input\n          v-model=\"queryParams.moduleCode\"\n          placeholder=\"请输入模块代码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"链接类型\" prop=\"linkType\">\n        <el-select v-model=\"queryParams.linkType\" placeholder=\"请选择链接类型\" clearable>\n          <el-option label=\"内部页面\" value=\"1\" />\n          <el-option label=\"外部链接\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\n          <el-option label=\"正常\" value=\"0\" />\n          <el-option label=\"停用\" value=\"1\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['config:homemodule:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['config:homemodule:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['config:homemodule:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['config:homemodule:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"homeModuleList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"70\" />\n      <el-table-column label=\"模块图标\" align=\"center\" prop=\"moduleIcon\" width=\"90\">\n        <template slot-scope=\"scope\">\n          <image-preview v-if=\"scope.row.moduleIcon\" :src=\"scope.row.moduleIcon\" :width=\"50\" :height=\"50\"/>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"模块名称\" align=\"center\" prop=\"moduleName\" width=\"120\" show-overflow-tooltip />\n      <el-table-column label=\"模块代码\" align=\"center\" prop=\"moduleCode\" width=\"150\" show-overflow-tooltip />\n      <el-table-column label=\"链接类型\" align=\"center\" prop=\"linkType\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.linkType === '1'\" type=\"primary\" size=\"mini\">内部页面</el-tag>\n          <el-tag v-else-if=\"scope.row.linkType === '2'\" type=\"warning\" size=\"mini\">外部链接</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"跳转地址\" align=\"center\" show-overflow-tooltip min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.linkType === '1'\">{{ scope.row.moduleUrl || '-' }}</span>\n          <span v-else-if=\"scope.row.linkType === '2'\">{{ scope.row.externalUrl || '-' }}</span>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"110\">\n        <template slot-scope=\"scope\">\n          <el-input-number\n            v-model=\"scope.row.sortOrder\"\n            :min=\"0\"\n            :max=\"9999\"\n            size=\"mini\"\n            controls-position=\"right\"\n            @change=\"handleSortOrderChange(scope.row)\"\n            style=\"width: 90px;\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"90\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.status === '0'\" type=\"success\" size=\"mini\">正常</el-tag>\n          <el-tag v-else type=\"danger\" size=\"mini\">停用</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"130\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['config:homemodule:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['config:homemodule:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改小程序首页功能模块对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"模块名称\" prop=\"moduleName\">\n          <el-input v-model=\"form.moduleName\" placeholder=\"请输入模块名称\" maxlength=\"100\" />\n        </el-form-item>\n        <el-form-item label=\"模块代码\" prop=\"moduleCode\">\n          <el-input\n            v-model=\"form.moduleCode\"\n            placeholder=\"请输入模块代码\"\n            maxlength=\"50\"\n            :disabled=\"form.id != null\"\n          />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            模块代码用于系统内部识别，必须唯一，创建后不可修改\n          </div>\n        </el-form-item>\n        <el-form-item label=\"模块图标\" prop=\"moduleIcon\">\n          <image-upload v-model=\"form.moduleIcon\"/>\n        </el-form-item>\n        <el-form-item label=\"链接类型\" prop=\"linkType\">\n          <el-radio-group v-model=\"form.linkType\" @change=\"handleLinkTypeChange\">\n            <el-radio label=\"1\">内部页面</el-radio>\n            <el-radio label=\"2\">外部链接</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item v-if=\"form.linkType === '1'\" label=\"内部链接\" prop=\"moduleUrl\">\n          <el-input v-model=\"form.moduleUrl\" placeholder=\"请输入小程序页面路径，如：/pages/home/<USER>\" />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            小程序内部页面路径，以 / 开头\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"form.linkType === '2'\" label=\"外部链接\" prop=\"externalUrl\">\n          <el-input v-model=\"form.externalUrl\" placeholder=\"请输入完整的URL地址，如：https://www.example.com\" />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            外部网站链接，需要包含 http:// 或 https://\n          </div>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\n          <el-input-number v-model=\"form.sortOrder\" :min=\"0\" :max=\"9999\" />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            数字越小越靠前显示\n          </div>\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio label=\"0\">正常</el-radio>\n            <el-radio label=\"1\">停用</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listHomeModule, getHomeModule, delHomeModule, addHomeModule, updateHomeModule, checkModuleCodeUnique } from \"@/api/miniapp/homemodule\";\n\nexport default {\n  name: \"HomeModule\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 小程序首页功能模块表格数据\n      homeModuleList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        moduleName: null,\n        moduleCode: null,\n        linkType: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 排序防抖定时器\n      sortOrderTimer: null,\n      // 表单校验\n      rules: {\n        moduleName: [\n          { required: true, message: \"模块名称不能为空\", trigger: \"blur\" },\n          { min: 1, max: 100, message: \"模块名称长度必须介于 1 和 100 之间\", trigger: \"blur\" }\n        ],\n        moduleCode: [\n          { required: true, message: \"模块代码不能为空\", trigger: \"blur\" },\n          { min: 1, max: 50, message: \"模块代码长度必须介于 1 和 50 之间\", trigger: \"blur\" },\n          { validator: this.validateModuleCode, trigger: \"blur\" }\n        ],\n        linkType: [\n          { required: true, message: \"链接类型不能为空\", trigger: \"change\" }\n        ],\n        moduleUrl: [\n          { validator: this.validateModuleUrl, trigger: \"blur\" }\n        ],\n        externalUrl: [\n          { validator: this.validateExternalUrl, trigger: \"blur\" }\n        ],\n        status: [\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  beforeDestroy() {\n    // 清理定时器\n    if (this.sortOrderTimer) {\n      clearTimeout(this.sortOrderTimer);\n    }\n  },\n  methods: {\n    /** 查询小程序首页功能模块列表 */\n    getList() {\n      this.loading = true;\n      listHomeModule(this.queryParams).then(response => {\n        this.homeModuleList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        moduleName: null,\n        moduleIcon: null,\n        moduleCode: null,\n        moduleUrl: null,\n        linkType: \"1\",\n        externalUrl: null,\n        sortOrder: 0,\n        status: \"0\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加小程序首页功能模块\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getHomeModule(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改小程序首页功能模块\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateHomeModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addHomeModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除小程序首页功能模块编号为\"' + ids + '\"的数据项？').then(function() {\n        return delHomeModule(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/homemodule/export', {\n        ...this.queryParams\n      }, `homemodule_${new Date().getTime()}.xlsx`)\n    },\n    /** 链接类型改变处理 */\n    handleLinkTypeChange(value) {\n      // 清空相关字段\n      if (value === '1') {\n        this.form.externalUrl = null;\n      } else if (value === '2') {\n        this.form.moduleUrl = null;\n      }\n    },\n    /** 排序值改变处理 */\n    handleSortOrderChange(row) {\n      // 防抖处理，避免频繁请求\n      if (this.sortOrderTimer) {\n        clearTimeout(this.sortOrderTimer);\n      }\n      this.sortOrderTimer = setTimeout(() => {\n        const updateData = {\n          id: row.id,\n          moduleName: row.moduleName,\n          moduleIcon: row.moduleIcon,\n          moduleCode: row.moduleCode,\n          moduleUrl: row.moduleUrl,\n          linkType: row.linkType,\n          externalUrl: row.externalUrl,\n          sortOrder: row.sortOrder,\n          status: row.status,\n          remark: row.remark\n        };\n        updateHomeModule(updateData).then(() => {\n          this.$modal.msgSuccess(\"排序更新成功\");\n          this.getList(); // 重新加载列表以显示最新排序\n        }).catch(() => {\n          this.$modal.msgError(\"排序更新失败\");\n          this.getList(); // 失败时也重新加载，恢复原始数据\n        });\n      }, 800); // 800ms防抖\n    },\n    /** 模块代码校验 */\n    validateModuleCode(rule, value, callback) {\n      if (value) {\n        const data = {\n          id: this.form.id,\n          moduleCode: value\n        };\n        checkModuleCodeUnique(data).then(response => {\n          if (response.data) {\n            callback();\n          } else {\n            callback(new Error(\"模块代码已存在\"));\n          }\n        }).catch(() => {\n          callback(new Error(\"校验失败\"));\n        });\n      } else {\n        callback();\n      }\n    },\n    /** 内部链接校验 */\n    validateModuleUrl(rule, value, callback) {\n      if (this.form.linkType === '1') {\n        if (!value) {\n          callback(new Error(\"内部链接不能为空\"));\n        } else if (!value.startsWith('/')) {\n          callback(new Error(\"内部链接必须以 / 开头\"));\n        } else {\n          callback();\n        }\n      } else {\n        callback();\n      }\n    },\n    /** 外部链接校验 */\n    validateExternalUrl(rule, value, callback) {\n      if (this.form.linkType === '2') {\n        if (!value) {\n          callback(new Error(\"外部链接不能为空\"));\n        } else if (!/^https?:\\/\\/.+/.test(value)) {\n          callback(new Error(\"外部链接必须以 http:// 或 https:// 开头\"));\n        } else {\n          callback();\n        }\n      } else {\n        callback();\n      }\n    }\n  }\n};\n</script>\n"]}]}