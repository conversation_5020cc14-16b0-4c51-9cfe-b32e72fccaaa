{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754295969458}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RIb21lTW9kdWxlLCBnZXRIb21lTW9kdWxlLCBkZWxIb21lTW9kdWxlLCBhZGRIb21lTW9kdWxlLCB1cGRhdGVIb21lTW9kdWxlLCBjaGVja01vZHVsZUNvZGVVbmlxdWUgfSBmcm9tICJAL2FwaS9taW5pYXBwL2hvbWVtb2R1bGUiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJIb21lTW9kdWxlIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdl+ihqOagvOaVsOaNrgogICAgICBob21lTW9kdWxlTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgbW9kdWxlTmFtZTogbnVsbCwKICAgICAgICBtb2R1bGVDb2RlOiBudWxsLAogICAgICAgIGxpbmtUeXBlOiBudWxsLAogICAgICAgIHN0YXR1czogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOaOkuW6j+mYsuaKluWumuaXtuWZqAogICAgICBzb3J0T3JkZXJUaW1lcjogbnVsbCwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgbW9kdWxlTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaooeWdl+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDEsIG1heDogMTAwLCBtZXNzYWdlOiAi5qih5Z2X5ZCN56ew6ZW/5bqm5b+F6aG75LuL5LqOIDEg5ZKMIDEwMCDkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgbW9kdWxlQ29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaooeWdl+S7o+eggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgICAgeyBtaW46IDEsIG1heDogNTAsIG1lc3NhZ2U6ICLmqKHlnZfku6PnoIHplb/luqblv4Xpobvku4vkuo4gMSDlkowgNTAg5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZU1vZHVsZUNvZGUsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBsaW5rVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumTvuaOpeexu+Wei+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIG1vZHVsZVVybDogWwogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVNb2R1bGVVcmwsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBleHRlcm5hbFVybDogWwogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVFeHRlcm5hbFVybCwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHN0YXR1czogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueKtuaAgeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBiZWZvcmVEZXN0cm95KCkgewogICAgLy8g5riF55CG5a6a5pe25ZmoCiAgICBpZiAodGhpcy5zb3J0T3JkZXJUaW1lcikgewogICAgICBjbGVhclRpbWVvdXQodGhpcy5zb3J0T3JkZXJUaW1lcik7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5bCP56iL5bqP6aaW6aG15Yqf6IO95qih5Z2X5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0SG9tZU1vZHVsZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmhvbWVNb2R1bGVMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBtb2R1bGVOYW1lOiBudWxsLAogICAgICAgIG1vZHVsZUljb246IG51bGwsCiAgICAgICAgbW9kdWxlQ29kZTogbnVsbCwKICAgICAgICBtb2R1bGVVcmw6IG51bGwsCiAgICAgICAgbGlua1R5cGU6ICIxIiwKICAgICAgICBleHRlcm5hbFVybDogbnVsbCwKICAgICAgICBzb3J0T3JkZXI6IDAsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgcmVtYXJrOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdlyI7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzCiAgICAgIGdldEhvbWVNb2R1bGUoaWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdlyI7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgdXBkYXRlSG9tZU1vZHVsZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZEhvbWVNb2R1bGUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5bCP56iL5bqP6aaW6aG15Yqf6IO95qih5Z2X57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7CiAgICAgICAgcmV0dXJuIGRlbEhvbWVNb2R1bGUoaWRzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ21pbmlhcHAvaG9tZW1vZHVsZS9leHBvcnQnLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcwogICAgICB9LCBgaG9tZW1vZHVsZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0sCiAgICAvKiog6ZO+5o6l57G75Z6L5pS55Y+Y5aSE55CGICovCiAgICBoYW5kbGVMaW5rVHlwZUNoYW5nZSh2YWx1ZSkgewogICAgICAvLyDmuIXnqbrnm7jlhbPlrZfmrrUKICAgICAgaWYgKHZhbHVlID09PSAnMScpIHsKICAgICAgICB0aGlzLmZvcm0uZXh0ZXJuYWxVcmwgPSBudWxsOwogICAgICB9IGVsc2UgaWYgKHZhbHVlID09PSAnMicpIHsKICAgICAgICB0aGlzLmZvcm0ubW9kdWxlVXJsID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIC8qKiDmjpLluo/lgLzmlLnlj5jlpITnkIYgKi8KICAgIGhhbmRsZVNvcnRPcmRlckNoYW5nZShyb3cpIHsKICAgICAgLy8g6Ziy5oqW5aSE55CG77yM6YG/5YWN6aKR57mB6K+35rGCCiAgICAgIGlmICh0aGlzLnNvcnRPcmRlclRpbWVyKSB7CiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuc29ydE9yZGVyVGltZXIpOwogICAgICB9CiAgICAgIHRoaXMuc29ydE9yZGVyVGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICBjb25zdCB1cGRhdGVEYXRhID0gewogICAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICAgIG1vZHVsZU5hbWU6IHJvdy5tb2R1bGVOYW1lLAogICAgICAgICAgbW9kdWxlSWNvbjogcm93Lm1vZHVsZUljb24sCiAgICAgICAgICBtb2R1bGVDb2RlOiByb3cubW9kdWxlQ29kZSwKICAgICAgICAgIG1vZHVsZVVybDogcm93Lm1vZHVsZVVybCwKICAgICAgICAgIGxpbmtUeXBlOiByb3cubGlua1R5cGUsCiAgICAgICAgICBleHRlcm5hbFVybDogcm93LmV4dGVybmFsVXJsLAogICAgICAgICAgc29ydE9yZGVyOiByb3cuc29ydE9yZGVyLAogICAgICAgICAgc3RhdHVzOiByb3cuc3RhdHVzLAogICAgICAgICAgcmVtYXJrOiByb3cucmVtYXJrCiAgICAgICAgfTsKICAgICAgICB1cGRhdGVIb21lTW9kdWxlKHVwZGF0ZURhdGEpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o6S5bqP5pu05paw5oiQ5YqfIik7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsgLy8g6YeN5paw5Yqg6L295YiX6KGo5Lul5pi+56S65pyA5paw5o6S5bqPCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuaOkuW6j+abtOaWsOWksei0pSIpOwogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7IC8vIOWksei0peaXtuS5n+mHjeaWsOWKoOi9ve+8jOaBouWkjeWOn+Wni+aVsOaNrgogICAgICAgIH0pOwogICAgICB9LCA4MDApOyAvLyA4MDBtc+mYsuaKlgogICAgfSwKICAgIC8qKiDmqKHlnZfku6PnoIHmoKHpqowgKi8KICAgIHZhbGlkYXRlTW9kdWxlQ29kZShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHZhbHVlKSB7CiAgICAgICAgY29uc3QgZGF0YSA9IHsKICAgICAgICAgIGlkOiB0aGlzLmZvcm0uaWQsCiAgICAgICAgICBtb2R1bGVDb2RlOiB2YWx1ZQogICAgICAgIH07CiAgICAgICAgY2hlY2tNb2R1bGVDb2RlVW5pcXVlKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsKICAgICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5qih5Z2X5Luj56CB5bey5a2Y5ZyoIikpOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5qCh6aqM5aSx6LSlIikpOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog5YaF6YOo6ZO+5o6l5qCh6aqMICovCiAgICB2YWxpZGF0ZU1vZHVsZVVybChydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHRoaXMuZm9ybS5saW5rVHlwZSA9PT0gJzEnKSB7CiAgICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlhoXpg6jpk77mjqXkuI3og73kuLrnqboiKSk7CiAgICAgICAgfSBlbHNlIGlmICghdmFsdWUuc3RhcnRzV2l0aCgnLycpKSB7CiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWGhemDqOmTvuaOpeW/hemhu+S7pSAvIOW8gOWktCIpKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDlpJbpg6jpk77mjqXmoKHpqowgKi8KICAgIHZhbGlkYXRlRXh0ZXJuYWxVcmwocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIGlmICh0aGlzLmZvcm0ubGlua1R5cGUgPT09ICcyJykgewogICAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5aSW6YOo6ZO+5o6l5LiN6IO95Li656m6IikpOwogICAgICAgIH0gZWxzZSBpZiAoIS9eaHR0cHM/OlwvXC8uKy8udGVzdCh2YWx1ZSkpIHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5aSW6YOo6ZO+5o6l5b+F6aG75LulIGh0dHA6Ly8g5oiWIGh0dHBzOi8vIOW8gOWktCIpKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY2FsbGJhY2soKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfQogIH0KfTsK"}, null]}