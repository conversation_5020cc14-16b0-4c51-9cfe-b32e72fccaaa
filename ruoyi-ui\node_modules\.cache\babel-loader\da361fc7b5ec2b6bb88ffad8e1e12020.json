{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1754297908532}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>Wtl+espuS4suWIhumalAogICAgbGlzdFRvU3RyaW5nOiBmdW5jdGlvbiBsaXN0VG9TdHJpbmcobGlzdCwgc2VwYXJhdG9yKSB7CiAgICAgIHZhciBzdHJzID0gIiI7CiAgICAgIHNlcGFyYXRvciA9IHNlcGFyYXRvciB8fCAiLCI7CiAgICAgIGZvciAodmFyIGkgaW4gbGlzdCkgewogICAgICAgIGlmIChsaXN0W2ldLnVybCkgewogICAgICAgICAgLy8g5L+d5a2Y5a6M5pW055qEVVJM77yM5LiN56e76ZmkYmFzZVVybOWJjee8gAogICAgICAgICAgc3RycyArPSBsaXN0W2ldLnVybCArIHNlcGFyYXRvcjsKICAgICAgICB9CiAgICAgIH0KICAgICAgcmV0dXJuIHN0cnMgIT0gJycgPyBzdHJzLnN1YnN0cmluZygwLCBzdHJzLmxlbmd0aCAtIDEpIDogJyc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_auth", "require", "_validate", "_sortablejs", "_interopRequireDefault", "props", "value", "String", "Object", "Array", "action", "type", "default", "data", "limit", "Number", "fileSize", "fileType", "isShowTip", "Boolean", "disabled", "drag", "number", "uploadList", "dialogImageUrl", "dialogVisible", "hideUpload", "baseUrl", "process", "env", "VUE_APP_BASE_API", "uploadImgUrl", "headers", "Authorization", "getToken", "fileList", "mounted", "_this", "$nextTick", "_this$$refs$imageUplo", "element", "$refs", "imageUpload", "$el", "querySelector", "Sortable", "create", "onEnd", "evt", "movedItem", "splice", "oldIndex", "newIndex", "$emit", "listToString", "watch", "handler", "val", "_this2", "list", "isArray", "split", "map", "item", "indexOf", "isExternal", "name", "url", "deep", "immediate", "computed", "showTip", "methods", "handleBeforeUpload", "file", "isImg", "length", "fileExtension", "lastIndexOf", "slice", "some", "$modal", "msgError", "concat", "join", "includes", "isLt", "size", "loading", "handleExceed", "handleUploadSuccess", "res", "code", "push", "fileName", "uploadedSuccessfully", "closeLoading", "msg", "handleRemove", "handleDelete", "findex", "f", "handleUploadError", "handlePictureCardPreview", "separator", "strs", "i", "substring"], "sources": ["src/components/ImageUpload/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :disabled=\"disabled\"\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :data=\"data\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      ref=\"imageUpload\"\r\n      :on-remove=\"handleDelete\"\r\n      :show-file-list=\"true\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip && !disabled\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\"\r\nimport { isExternal } from \"@/utils/validate\"\r\nimport Sortable from 'sortablejs'\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 上传接口地址\r\n    action: {\r\n      type: String,\r\n      default: \"/common/upload\"\r\n    },\r\n    // 上传携带的参数\r\n    data: {\r\n      type: Object\r\n    },\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"]\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 禁用组件（仅查看图片）\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 拖动排序\r\n    drag: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    if (this.drag && !this.disabled) {\r\n      this.$nextTick(() => {\r\n        const element = this.$refs.imageUpload?.$el?.querySelector('.el-upload-list')\r\n        Sortable.create(element, {\r\n          onEnd: (evt) => {\r\n            const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]\r\n            this.fileList.splice(evt.newIndex, 0, movedItem)\r\n            this.$emit(\"input\", this.listToString(this.fileList))\r\n          }\r\n        })\r\n      })\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',')\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {\r\n                  item = { name: this.baseUrl + item, url: this.baseUrl + item }\r\n              } else {\r\n                  item = { name: item, url: item }\r\n              }\r\n            }\r\n            return item\r\n          })\r\n        } else {\r\n          this.fileList = []\r\n          return []\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize)\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\"\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1)\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true\r\n          return false\r\n        })\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}图片格式文件!`)\r\n        return false\r\n      }\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!')\r\n        return false\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)\r\n          return false\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\")\r\n      this.number++\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        // fileName，相对路径\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName })\r\n        this.uploadedSuccessfully()\r\n      } else {\r\n        this.number--\r\n        this.$modal.closeLoading()\r\n        this.$modal.msgError(res.msg)\r\n        this.$refs.imageUpload.handleRemove(file)\r\n        this.uploadedSuccessfully()\r\n      }\r\n    },\r\n    // 删除图片\r\n    handleDelete(file) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name)\r\n      if (findex > -1) {\r\n        this.fileList.splice(findex, 1)\r\n        this.$emit(\"input\", this.listToString(this.fileList))\r\n      }\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\")\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList)\r\n        this.uploadList = []\r\n        this.number = 0\r\n        this.$emit(\"input\", this.listToString(this.fileList))\r\n        this.$modal.closeLoading()\r\n      }\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\"\r\n      separator = separator || \",\"\r\n      for (let i in list) {\r\n        if (list[i].url) {\r\n          // 保存完整的URL，不移除baseUrl前缀\r\n          strs += list[i].url + separator\r\n        }\r\n      }\r\n      return strs != '' ? strs.substring(0, strs.length - 1) : ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n  display: none;\r\n}\r\n\r\n::v-deep .el-upload-list--picture-card.is-disabled + .el-upload--picture-card {\r\n  display: none !important;\r\n}\r\n\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n  transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n  opacity: 0;\r\n  transform: translateY(0);\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA+CA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAI,KAAA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,EAAAJ,MAAA;MACAK,OAAA;IACA;IACA;IACAC,IAAA;MACAF,IAAA,EAAAH;IACA;IACA;IACAM,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAI,QAAA;MACAL,IAAA,EAAAI,MAAA;MACAH,OAAA;IACA;IACA;IACAK,QAAA;MACAN,IAAA,EAAAF,KAAA;MACAG,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACA;IACAM,SAAA;MACAP,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAQ,QAAA;MACAT,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;IACA;IACAS,IAAA;MACAV,IAAA,EAAAQ,OAAA;MACAP,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAS,MAAA;MACAC,UAAA;MACAC,cAAA;MACAC,aAAA;MACAC,UAAA;MACAC,OAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,YAAA,EAAAH,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAApB,MAAA;MAAA;MACAsB,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,SAAAhB,IAAA,UAAAD,QAAA;MACA,KAAAkB,SAAA;QAAA,IAAAC,qBAAA;QACA,IAAAC,OAAA,IAAAD,qBAAA,GAAAF,KAAA,CAAAI,KAAA,CAAAC,WAAA,cAAAH,qBAAA,gBAAAA,qBAAA,GAAAA,qBAAA,CAAAI,GAAA,cAAAJ,qBAAA,uBAAAA,qBAAA,CAAAK,aAAA;QACAC,mBAAA,CAAAC,MAAA,CAAAN,OAAA;UACAO,KAAA,WAAAA,MAAAC,GAAA;YACA,IAAAC,SAAA,GAAAZ,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAG,QAAA;YACAd,KAAA,CAAAF,QAAA,CAAAe,MAAA,CAAAF,GAAA,CAAAI,QAAA,KAAAH,SAAA;YACAZ,KAAA,CAAAgB,KAAA,UAAAhB,KAAA,CAAAiB,YAAA,CAAAjB,KAAA,CAAAF,QAAA;UACA;QACA;MACA;IACA;EACA;EACAoB,KAAA;IACAjD,KAAA;MACAkD,OAAA,WAAAA,QAAAC,GAAA;QAAA,IAAAC,MAAA;QACA,IAAAD,GAAA;UACA;UACA,IAAAE,IAAA,GAAAlD,KAAA,CAAAmD,OAAA,CAAAH,GAAA,IAAAA,GAAA,QAAAnD,KAAA,CAAAuD,KAAA;UACA;UACA,KAAA1B,QAAA,GAAAwB,IAAA,CAAAG,GAAA,WAAAC,IAAA;YACA,WAAAA,IAAA;cACA,IAAAA,IAAA,CAAAC,OAAA,CAAAN,MAAA,CAAA/B,OAAA,iBAAAsC,oBAAA,EAAAF,IAAA;gBACAA,IAAA;kBAAAG,IAAA,EAAAR,MAAA,CAAA/B,OAAA,GAAAoC,IAAA;kBAAAI,GAAA,EAAAT,MAAA,CAAA/B,OAAA,GAAAoC;gBAAA;cACA;gBACAA,IAAA;kBAAAG,IAAA,EAAAH,IAAA;kBAAAI,GAAA,EAAAJ;gBAAA;cACA;YACA;YACA,OAAAA,IAAA;UACA;QACA;UACA,KAAA5B,QAAA;UACA;QACA;MACA;MACAiC,IAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAArD,SAAA,UAAAD,QAAA,SAAAD,QAAA;IACA;EACA;EACAwD,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,KAAA;MACA,SAAA1D,QAAA,CAAA2D,MAAA;QACA,IAAAC,aAAA;QACA,IAAAH,IAAA,CAAAR,IAAA,CAAAY,WAAA;UACAD,aAAA,GAAAH,IAAA,CAAAR,IAAA,CAAAa,KAAA,CAAAL,IAAA,CAAAR,IAAA,CAAAY,WAAA;QACA;QACAH,KAAA,QAAA1D,QAAA,CAAA+D,IAAA,WAAArE,IAAA;UACA,IAAA+D,IAAA,CAAA/D,IAAA,CAAAqD,OAAA,CAAArD,IAAA;UACA,IAAAkE,aAAA,IAAAA,aAAA,CAAAb,OAAA,CAAArD,IAAA;UACA;QACA;MACA;QACAgE,KAAA,GAAAD,IAAA,CAAA/D,IAAA,CAAAqD,OAAA;MACA;MAEA,KAAAW,KAAA;QACA,KAAAM,MAAA,CAAAC,QAAA,sEAAAC,MAAA,MAAAlE,QAAA,CAAAmE,IAAA;QACA;MACA;MACA,IAAAV,IAAA,CAAAR,IAAA,CAAAmB,QAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA;QACA;MACA;MACA,SAAAlE,QAAA;QACA,IAAAsE,IAAA,GAAAZ,IAAA,CAAAa,IAAA,sBAAAvE,QAAA;QACA,KAAAsE,IAAA;UACA,KAAAL,MAAA,CAAAC,QAAA,6EAAAC,MAAA,MAAAnE,QAAA;UACA;QACA;MACA;MACA,KAAAiE,MAAA,CAAAO,OAAA;MACA,KAAAlE,MAAA;IACA;IACA;IACAmE,YAAA,WAAAA,aAAA;MACA,KAAAR,MAAA,CAAAC,QAAA,iEAAAC,MAAA,MAAArE,KAAA;IACA;IACA;IACA4E,mBAAA,WAAAA,oBAAAC,GAAA,EAAAjB,IAAA;MACA,IAAAiB,GAAA,CAAAC,IAAA;QACA;QACA,KAAArE,UAAA,CAAAsE,IAAA;UAAA3B,IAAA,EAAAyB,GAAA,CAAAG,QAAA;UAAA3B,GAAA,EAAAwB,GAAA,CAAAG;QAAA;QACA,KAAAC,oBAAA;MACA;QACA,KAAAzE,MAAA;QACA,KAAA2D,MAAA,CAAAe,YAAA;QACA,KAAAf,MAAA,CAAAC,QAAA,CAAAS,GAAA,CAAAM,GAAA;QACA,KAAAxD,KAAA,CAAAC,WAAA,CAAAwD,YAAA,CAAAxB,IAAA;QACA,KAAAqB,oBAAA;MACA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAzB,IAAA;MACA,IAAA0B,MAAA,QAAAjE,QAAA,CAAA2B,GAAA,WAAAuC,CAAA;QAAA,OAAAA,CAAA,CAAAnC,IAAA;MAAA,GAAAF,OAAA,CAAAU,IAAA,CAAAR,IAAA;MACA,IAAAkC,MAAA;QACA,KAAAjE,QAAA,CAAAe,MAAA,CAAAkD,MAAA;QACA,KAAA/C,KAAA,eAAAC,YAAA,MAAAnB,QAAA;MACA;IACA;IACA;IACAmE,iBAAA,WAAAA,kBAAA;MACA,KAAArB,MAAA,CAAAC,QAAA;MACA,KAAAD,MAAA,CAAAe,YAAA;IACA;IACA;IACAD,oBAAA,WAAAA,qBAAA;MACA,SAAAzE,MAAA,aAAAC,UAAA,CAAAqD,MAAA,UAAAtD,MAAA;QACA,KAAAa,QAAA,QAAAA,QAAA,CAAAgD,MAAA,MAAA5D,UAAA;QACA,KAAAA,UAAA;QACA,KAAAD,MAAA;QACA,KAAA+B,KAAA,eAAAC,YAAA,MAAAnB,QAAA;QACA,KAAA8C,MAAA,CAAAe,YAAA;MACA;IACA;IACA;IACAO,wBAAA,WAAAA,yBAAA7B,IAAA;MACA,KAAAlD,cAAA,GAAAkD,IAAA,CAAAP,GAAA;MACA,KAAA1C,aAAA;IACA;IACA;IACA6B,YAAA,WAAAA,aAAAK,IAAA,EAAA6C,SAAA;MACA,IAAAC,IAAA;MACAD,SAAA,GAAAA,SAAA;MACA,SAAAE,CAAA,IAAA/C,IAAA;QACA,IAAAA,IAAA,CAAA+C,CAAA,EAAAvC,GAAA;UACA;UACAsC,IAAA,IAAA9C,IAAA,CAAA+C,CAAA,EAAAvC,GAAA,GAAAqC,SAAA;QACA;MACA;MACA,OAAAC,IAAA,SAAAA,IAAA,CAAAE,SAAA,IAAAF,IAAA,CAAA7B,MAAA;IACA;EACA;AACA", "ignoreList": []}]}