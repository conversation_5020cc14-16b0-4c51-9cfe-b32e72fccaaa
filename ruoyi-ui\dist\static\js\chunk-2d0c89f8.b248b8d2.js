(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c89f8"],{5656:function(e,t,r){"use strict";r.r(t);var l=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"模块名称",prop:"moduleName"}},[r("el-input",{attrs:{placeholder:"请输入模块名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.moduleName,callback:function(t){e.$set(e.queryParams,"moduleName",t)},expression:"queryParams.moduleName"}})],1),r("el-form-item",{attrs:{label:"模块代码",prop:"moduleCode"}},[r("el-input",{attrs:{placeholder:"请输入模块代码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.moduleCode,callback:function(t){e.$set(e.queryParams,"moduleCode",t)},expression:"queryParams.moduleCode"}})],1),r("el-form-item",{attrs:{label:"链接类型",prop:"linkType"}},[r("el-select",{attrs:{placeholder:"请选择链接类型",clearable:""},model:{value:e.queryParams.linkType,callback:function(t){e.$set(e.queryParams,"linkType",t)},expression:"queryParams.linkType"}},[r("el-option",{attrs:{label:"内部页面",value:"1"}}),r("el-option",{attrs:{label:"外部链接",value:"2"}})],1)],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[r("el-option",{attrs:{label:"正常",value:"0"}}),r("el-option",{attrs:{label:"停用",value:"1"}})],1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["config:homemodule:add"],expression:"['config:homemodule:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["config:homemodule:edit"],expression:"['config:homemodule:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["config:homemodule:remove"],expression:"['config:homemodule:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["config:homemodule:export"],expression:"['config:homemodule:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.homeModuleList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"ID",align:"center",prop:"id",width:"70"}}),r("el-table-column",{attrs:{label:"模块图标",align:"center",prop:"moduleIcon",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.moduleIcon?r("image-preview",{attrs:{src:t.row.moduleIcon,width:50,height:50}}):r("span",[e._v("-")])]}}])}),r("el-table-column",{attrs:{label:"模块名称",align:"center",prop:"moduleName",width:"120","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"模块代码",align:"center",prop:"moduleCode",width:"150","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{label:"链接类型",align:"center",prop:"linkType",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["1"===t.row.linkType?r("el-tag",{attrs:{type:"primary",size:"mini"}},[e._v("内部页面")]):"2"===t.row.linkType?r("el-tag",{attrs:{type:"warning",size:"mini"}},[e._v("外部链接")]):e._e()]}}])}),r("el-table-column",{attrs:{label:"跳转地址",align:"center","show-overflow-tooltip":"","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return["1"===t.row.linkType?r("span",[e._v(e._s(t.row.moduleUrl||"-"))]):"2"===t.row.linkType?r("span",[e._v(e._s(t.row.externalUrl||"-"))]):r("span",[e._v("-")])]}}])}),r("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticStyle:{width:"90px"},attrs:{min:0,max:9999,size:"mini","controls-position":"right"},on:{change:function(r){return e.handleSortOrderChange(t.row)}},model:{value:t.row.sortOrder,callback:function(r){e.$set(t.row,"sortOrder",r)},expression:"scope.row.sortOrder"}})]}}])}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"===t.row.status?r("el-tag",{attrs:{type:"success",size:"mini"}},[e._v("正常")]):r("el-tag",{attrs:{type:"danger",size:"mini"}},[e._v("停用")])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center",width:"130","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["config:homemodule:edit"],expression:"['config:homemodule:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["config:homemodule:remove"],expression:"['config:homemodule:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"模块名称",prop:"moduleName"}},[r("el-input",{attrs:{placeholder:"请输入模块名称",maxlength:"100"},model:{value:e.form.moduleName,callback:function(t){e.$set(e.form,"moduleName",t)},expression:"form.moduleName"}})],1),r("el-form-item",{attrs:{label:"模块代码",prop:"moduleCode"}},[r("el-input",{attrs:{placeholder:"请输入模块代码",maxlength:"50",disabled:null!=e.form.id},model:{value:e.form.moduleCode,callback:function(t){e.$set(e.form,"moduleCode",t)},expression:"form.moduleCode"}}),r("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[e._v(" 模块代码用于系统内部识别，必须唯一，创建后不可修改 ")])],1),r("el-form-item",{attrs:{label:"模块图标",prop:"moduleIcon"}},[r("image-upload",{model:{value:e.form.moduleIcon,callback:function(t){e.$set(e.form,"moduleIcon",t)},expression:"form.moduleIcon"}})],1),r("el-form-item",{attrs:{label:"链接类型",prop:"linkType"}},[r("el-radio-group",{on:{change:e.handleLinkTypeChange},model:{value:e.form.linkType,callback:function(t){e.$set(e.form,"linkType",t)},expression:"form.linkType"}},[r("el-radio",{attrs:{label:"1"}},[e._v("内部页面")]),r("el-radio",{attrs:{label:"2"}},[e._v("外部链接")])],1)],1),"1"===e.form.linkType?r("el-form-item",{attrs:{label:"内部链接",prop:"moduleUrl"}},[r("el-input",{attrs:{placeholder:"请输入小程序页面路径，如：/pages/home/<USER>"},model:{value:e.form.moduleUrl,callback:function(t){e.$set(e.form,"moduleUrl",t)},expression:"form.moduleUrl"}}),r("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[e._v(" 小程序内部页面路径，以 / 开头 ")])],1):e._e(),"2"===e.form.linkType?r("el-form-item",{attrs:{label:"外部链接",prop:"externalUrl"}},[r("el-input",{attrs:{placeholder:"请输入完整的URL地址，如：https://www.example.com"},model:{value:e.form.externalUrl,callback:function(t){e.$set(e.form,"externalUrl",t)},expression:"form.externalUrl"}}),r("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[e._v(" 外部网站链接，需要包含 http:// 或 https:// ")])],1):e._e(),r("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[r("el-input-number",{attrs:{min:0,max:9999},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}}),r("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[e._v(" 数字越小越靠前显示 ")])],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[r("el-radio",{attrs:{label:"0"}},[e._v("正常")]),r("el-radio",{attrs:{label:"1"}},[e._v("停用")])],1)],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],a=r("5530"),n=(r("d9e2"),r("d81d"),r("d3b7"),r("ac1f"),r("00b4"),r("2ca0"),r("0643"),r("a573"),r("b775"));function i(e){return Object(n["a"])({url:"/miniapp/homemodule/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/miniapp/homemodule/"+e,method:"get"})}function m(e){return Object(n["a"])({url:"/miniapp/homemodule",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/miniapp/homemodule",method:"put",data:e})}function d(e){return Object(n["a"])({url:"/miniapp/homemodule/"+e,method:"delete"})}function c(e){return Object(n["a"])({url:"/miniapp/homemodule/checkModuleCodeUnique",method:"post",data:e})}var p={name:"HomeModule",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,homeModuleList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,moduleName:null,moduleCode:null,linkType:null,status:null},form:{},sortOrderTimer:null,rules:{moduleName:[{required:!0,message:"模块名称不能为空",trigger:"blur"},{min:1,max:100,message:"模块名称长度必须介于 1 和 100 之间",trigger:"blur"}],moduleCode:[{required:!0,message:"模块代码不能为空",trigger:"blur"},{min:1,max:50,message:"模块代码长度必须介于 1 和 50 之间",trigger:"blur"},{validator:this.validateModuleCode,trigger:"blur"}],linkType:[{required:!0,message:"链接类型不能为空",trigger:"change"}],moduleUrl:[{validator:this.validateModuleUrl,trigger:"blur"}],externalUrl:[{validator:this.validateExternalUrl,trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},created:function(){this.getList()},beforeDestroy:function(){this.sortOrderTimer&&clearTimeout(this.sortOrderTimer)},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.homeModuleList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,moduleName:null,moduleIcon:null,moduleCode:null,moduleUrl:null,linkType:"1",externalUrl:null,sortOrder:0,status:"0",remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加小程序首页功能模块"},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;s(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改小程序首页功能模块"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):m(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除小程序首页功能模块编号为"'+r+'"的数据项？').then((function(){return d(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("miniapp/homemodule/export",Object(a["a"])({},this.queryParams),"homemodule_".concat((new Date).getTime(),".xlsx"))},handleLinkTypeChange:function(e){"1"===e?this.form.externalUrl=null:"2"===e&&(this.form.moduleUrl=null)},handleSortOrderChange:function(e){var t=this;this.sortOrderTimer&&clearTimeout(this.sortOrderTimer),this.sortOrderTimer=setTimeout((function(){var r={id:e.id,moduleName:e.moduleName,moduleIcon:e.moduleIcon,moduleCode:e.moduleCode,moduleUrl:e.moduleUrl,linkType:e.linkType,externalUrl:e.externalUrl,sortOrder:e.sortOrder,status:e.status,remark:e.remark};u(r).then((function(){t.$modal.msgSuccess("排序更新成功"),t.getList()})).catch((function(){t.$modal.msgError("排序更新失败"),t.getList()}))}),800)},validateModuleCode:function(e,t,r){if(t){var l={id:this.form.id,moduleCode:t};c(l).then((function(e){e.data?r():r(new Error("模块代码已存在"))})).catch((function(){r(new Error("校验失败"))}))}else r()},validateModuleUrl:function(e,t,r){"1"===this.form.linkType?t?t.startsWith("/")?r():r(new Error("内部链接必须以 / 开头")):r(new Error("内部链接不能为空")):r()},validateExternalUrl:function(e,t,r){"2"===this.form.linkType?t?/^https?:\/\/.+/.test(t)?r():r(new Error("外部链接必须以 http:// 或 https:// 开头")):r(new Error("外部链接不能为空")):r()}}},h=p,f=r("2877"),g=Object(f["a"])(h,l,o,!1,null,null,null);t["default"]=g.exports}}]);