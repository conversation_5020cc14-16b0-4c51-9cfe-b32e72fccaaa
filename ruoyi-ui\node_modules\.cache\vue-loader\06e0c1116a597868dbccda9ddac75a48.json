{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1754297908532}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ImageUpload", "sourcesContent": ["<template>\r\n  <div class=\"component-upload-image\">\r\n    <el-upload\r\n      multiple\r\n      :disabled=\"disabled\"\r\n      :action=\"uploadImgUrl\"\r\n      list-type=\"picture-card\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :data=\"data\"\r\n      :limit=\"limit\"\r\n      :on-error=\"handleUploadError\"\r\n      :on-exceed=\"handleExceed\"\r\n      ref=\"imageUpload\"\r\n      :on-remove=\"handleDelete\"\r\n      :show-file-list=\"true\"\r\n      :headers=\"headers\"\r\n      :file-list=\"fileList\"\r\n      :on-preview=\"handlePictureCardPreview\"\r\n      :class=\"{hide: this.fileList.length >= this.limit}\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n    </el-upload>\r\n\r\n    <!-- 上传提示 -->\r\n    <div class=\"el-upload__tip\" slot=\"tip\" v-if=\"showTip && !disabled\">\r\n      请上传\r\n      <template v-if=\"fileSize\"> 大小不超过 <b style=\"color: #f56c6c\">{{ fileSize }}MB</b> </template>\r\n      <template v-if=\"fileType\"> 格式为 <b style=\"color: #f56c6c\">{{ fileType.join(\"/\") }}</b> </template>\r\n      的文件\r\n    </div>\r\n\r\n    <el-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"预览\"\r\n      width=\"800\"\r\n      append-to-body\r\n    >\r\n      <img\r\n        :src=\"dialogImageUrl\"\r\n        style=\"display: block; max-width: 100%; margin: 0 auto\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\"\r\nimport { isExternal } from \"@/utils/validate\"\r\nimport Sortable from 'sortablejs'\r\n\r\nexport default {\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 上传接口地址\r\n    action: {\r\n      type: String,\r\n      default: \"/common/upload\"\r\n    },\r\n    // 上传携带的参数\r\n    data: {\r\n      type: Object\r\n    },\r\n    // 图片数量限制\r\n    limit: {\r\n      type: Number,\r\n      default: 5\r\n    },\r\n    // 大小限制(MB)\r\n    fileSize: {\r\n       type: Number,\r\n      default: 5\r\n    },\r\n    // 文件类型, 例如['png', 'jpg', 'jpeg']\r\n    fileType: {\r\n      type: Array,\r\n      default: () => [\"png\", \"jpg\", \"jpeg\"]\r\n    },\r\n    // 是否显示提示\r\n    isShowTip: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 禁用组件（仅查看图片）\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 拖动排序\r\n    drag: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      number: 0,\r\n      uploadList: [],\r\n      dialogImageUrl: \"\",\r\n      dialogVisible: false,\r\n      hideUpload: false,\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: \"Bearer \" + getToken(),\r\n      },\r\n      fileList: []\r\n    }\r\n  },\r\n  mounted() {\r\n    if (this.drag && !this.disabled) {\r\n      this.$nextTick(() => {\r\n        const element = this.$refs.imageUpload?.$el?.querySelector('.el-upload-list')\r\n        Sortable.create(element, {\r\n          onEnd: (evt) => {\r\n            const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]\r\n            this.fileList.splice(evt.newIndex, 0, movedItem)\r\n            this.$emit(\"input\", this.listToString(this.fileList))\r\n          }\r\n        })\r\n      })\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          // 首先将值转为数组\r\n          const list = Array.isArray(val) ? val : this.value.split(',')\r\n          // 然后将数组转为对象数组\r\n          this.fileList = list.map(item => {\r\n            if (typeof item === \"string\") {\r\n              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {\r\n                  item = { name: this.baseUrl + item, url: this.baseUrl + item }\r\n              } else {\r\n                  item = { name: item, url: item }\r\n              }\r\n            }\r\n            return item\r\n          })\r\n        } else {\r\n          this.fileList = []\r\n          return []\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示提示\r\n    showTip() {\r\n      return this.isShowTip && (this.fileType || this.fileSize)\r\n    },\r\n  },\r\n  methods: {\r\n    // 上传前loading加载\r\n    handleBeforeUpload(file) {\r\n      let isImg = false\r\n      if (this.fileType.length) {\r\n        let fileExtension = \"\"\r\n        if (file.name.lastIndexOf(\".\") > -1) {\r\n          fileExtension = file.name.slice(file.name.lastIndexOf(\".\") + 1)\r\n        }\r\n        isImg = this.fileType.some(type => {\r\n          if (file.type.indexOf(type) > -1) return true\r\n          if (fileExtension && fileExtension.indexOf(type) > -1) return true\r\n          return false\r\n        })\r\n      } else {\r\n        isImg = file.type.indexOf(\"image\") > -1\r\n      }\r\n\r\n      if (!isImg) {\r\n        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join(\"/\")}图片格式文件!`)\r\n        return false\r\n      }\r\n      if (file.name.includes(',')) {\r\n        this.$modal.msgError('文件名不正确，不能包含英文逗号!')\r\n        return false\r\n      }\r\n      if (this.fileSize) {\r\n        const isLt = file.size / 1024 / 1024 < this.fileSize\r\n        if (!isLt) {\r\n          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)\r\n          return false\r\n        }\r\n      }\r\n      this.$modal.loading(\"正在上传图片，请稍候...\")\r\n      this.number++\r\n    },\r\n    // 文件个数超出\r\n    handleExceed() {\r\n      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)\r\n    },\r\n    // 上传成功回调\r\n    handleUploadSuccess(res, file) {\r\n      if (res.code === 200) {\r\n        // fileName，相对路径\r\n        this.uploadList.push({ name: res.fileName, url: res.fileName })\r\n        this.uploadedSuccessfully()\r\n      } else {\r\n        this.number--\r\n        this.$modal.closeLoading()\r\n        this.$modal.msgError(res.msg)\r\n        this.$refs.imageUpload.handleRemove(file)\r\n        this.uploadedSuccessfully()\r\n      }\r\n    },\r\n    // 删除图片\r\n    handleDelete(file) {\r\n      const findex = this.fileList.map(f => f.name).indexOf(file.name)\r\n      if (findex > -1) {\r\n        this.fileList.splice(findex, 1)\r\n        this.$emit(\"input\", this.listToString(this.fileList))\r\n      }\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError(\"上传图片失败，请重试\")\r\n      this.$modal.closeLoading()\r\n    },\r\n    // 上传结束处理\r\n    uploadedSuccessfully() {\r\n      if (this.number > 0 && this.uploadList.length === this.number) {\r\n        this.fileList = this.fileList.concat(this.uploadList)\r\n        this.uploadList = []\r\n        this.number = 0\r\n        this.$emit(\"input\", this.listToString(this.fileList))\r\n        this.$modal.closeLoading()\r\n      }\r\n    },\r\n    // 预览\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n    // 对象转成指定字符串分隔\r\n    listToString(list, separator) {\r\n      let strs = \"\"\r\n      separator = separator || \",\"\r\n      for (let i in list) {\r\n        if (list[i].url) {\r\n          // 保存完整的URL，不移除baseUrl前缀\r\n          strs += list[i].url + separator\r\n        }\r\n      }\r\n      return strs != '' ? strs.substring(0, strs.length - 1) : ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n// .el-upload--picture-card 控制加号部分\r\n::v-deep.hide .el-upload--picture-card {\r\n  display: none;\r\n}\r\n\r\n::v-deep .el-upload-list--picture-card.is-disabled + .el-upload--picture-card {\r\n  display: none !important;\r\n}\r\n\r\n// 去掉动画效果\r\n::v-deep .el-list-enter-active,\r\n::v-deep .el-list-leave-active {\r\n  transition: all 0s;\r\n}\r\n\r\n::v-deep .el-list-enter, .el-list-leave-active {\r\n  opacity: 0;\r\n  transform: translateY(0);\r\n}\r\n</style>\r\n\r\n"]}]}