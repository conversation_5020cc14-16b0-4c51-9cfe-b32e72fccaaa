{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\test\\ImageUploadTest.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\test\\ImageUploadTest.vue", "mtime": 1754297883203}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ImageUpload", "_interopRequireDefault", "require", "name", "components", "ImageUpload", "data", "testValue1", "testValue2", "testValue3", "testValue4", "computed", "currentProtocol", "window", "location", "protocol", "currentHost", "host", "apiPrefix", "process", "env", "VUE_APP_BASE_API", "fullUrlExample", "testPath"], "sources": ["src/views/test/ImageUploadTest.vue"], "sourcesContent": ["<template>\n  <div class=\"test-container\">\n    <h2>ImageUpload组件测试</h2>\n    \n    <div class=\"test-section\">\n      <h3>测试场景1：相对路径（应该自动添加完整URL前缀）</h3>\n      <p>测试值：/profile/upload/2025/08/04/icon7_20250804164314A007.png</p>\n      <ImageUpload v-model=\"testValue1\" :limit=\"1\" />\n      <p>当前值：{{ testValue1 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试场景2：已包含baseUrl的路径</h3>\n      <p>测试值：/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png</p>\n      <ImageUpload v-model=\"testValue2\" :limit=\"1\" />\n      <p>当前值：{{ testValue2 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试场景3：完整URL</h3>\n      <p>测试值：http://localhost:8086/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png</p>\n      <ImageUpload v-model=\"testValue3\" :limit=\"1\" />\n      <p>当前值：{{ testValue3 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试场景4：外部URL</h3>\n      <p>测试值：https://example.com/image.jpg</p>\n      <ImageUpload v-model=\"testValue4\" :limit=\"1\" />\n      <p>当前值：{{ testValue4 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>URL构建测试</h3>\n      <p>当前协议：{{ currentProtocol }}</p>\n      <p>当前主机：{{ currentHost }}</p>\n      <p>API前缀：{{ apiPrefix }}</p>\n      <p>构建的完整URL示例：{{ fullUrlExample }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ImageUpload from '@/components/ImageUpload'\n\nexport default {\n  name: 'ImageUploadTest',\n  components: {\n    ImageUpload\n  },\n  data() {\n    return {\n      testValue1: '/profile/upload/2025/08/04/icon7_20250804164314A007.png',\n      testValue2: '/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png',\n      testValue3: 'http://localhost:8086/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png',\n      testValue4: 'https://example.com/image.jpg'\n    }\n  },\n  computed: {\n    currentProtocol() {\n      return window.location.protocol\n    },\n    currentHost() {\n      return window.location.host\n    },\n    apiPrefix() {\n      return process.env.VUE_APP_BASE_API\n    },\n    fullUrlExample() {\n      const testPath = '/profile/upload/2025/08/04/icon7_20250804164314A007.png'\n      return window.location.protocol + '//' + window.location.host + process.env.VUE_APP_BASE_API + testPath\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background-color: #fafafa;\n}\n\n.test-section h3 {\n  margin-top: 0;\n  color: #409eff;\n}\n\n.test-section p {\n  margin: 10px 0;\n  font-size: 14px;\n}\n</style>\n"], "mappings": ";;;;;;;AA2CA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;MACAC,UAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,eAAA,WAAAA,gBAAA;MACA,OAAAC,MAAA,CAAAC,QAAA,CAAAC,QAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,OAAAH,MAAA,CAAAC,QAAA,CAAAG,IAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,IAAAC,QAAA;MACA,OAAAV,MAAA,CAAAC,QAAA,CAAAC,QAAA,UAAAF,MAAA,CAAAC,QAAA,CAAAG,IAAA,GAAAE,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAAE,QAAA;IACA;EACA;AACA", "ignoreList": []}]}