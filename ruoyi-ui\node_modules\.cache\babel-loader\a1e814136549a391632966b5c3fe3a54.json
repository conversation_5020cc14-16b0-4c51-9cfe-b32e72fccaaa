{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\roadshow.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\roadshow.js", "mtime": 1754291071369}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listRoadshow", "query", "request", "url", "method", "params", "getRoadshow", "id", "addRoadshow", "data", "updateRoadshow", "delRoadshow", "getEnabledRoadshowList", "checkUniqueCodeUnique", "getRoadshowByCode", "uniqueCode", "getEnabledRoadshowListForApp"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/roadshow.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询创赛路演管理列表\nexport function listRoadshow(query) {\n  return request({\n    url: '/miniapp/roadshow/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询创赛路演管理详细\nexport function getRoadshow(id) {\n  return request({\n    url: '/miniapp/roadshow/' + id,\n    method: 'get'\n  })\n}\n\n// 新增创赛路演管理\nexport function addRoadshow(data) {\n  return request({\n    url: '/miniapp/roadshow',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改创赛路演管理\nexport function updateRoadshow(data) {\n  return request({\n    url: '/miniapp/roadshow',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除创赛路演管理\nexport function delRoadshow(id) {\n  return request({\n    url: '/miniapp/roadshow/' + id,\n    method: 'delete'\n  })\n}\n\n// 获取启用的创赛路演管理列表\nexport function getEnabledRoadshowList() {\n  return request({\n    url: '/miniapp/roadshow/enabled',\n    method: 'get'\n  })\n}\n\n// 校验唯一标识\nexport function checkUniqueCodeUnique(data) {\n  return request({\n    url: '/miniapp/roadshow/checkUniqueCodeUnique',\n    method: 'post',\n    data: data\n  })\n}\n\n// 根据唯一标识获取创赛路演详情（小程序端）\nexport function getRoadshowByCode(uniqueCode) {\n  return request({\n    url: '/miniapp/roadshow/app/getByCode/' + uniqueCode,\n    method: 'get'\n  })\n}\n\n// 获取启用的创赛路演列表（小程序端）\nexport function getEnabledRoadshowListForApp() {\n  return request({\n    url: '/miniapp/roadshow/app/getEnabledList',\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,EAAE,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,EAAE,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,EAAE;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,sBAAsBA,CAAA,EAAG;EACvC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,qBAAqBA,CAACJ,IAAI,EAAE;EAC1C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACC,UAAU,EAAE;EAC5C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGY,UAAU;IACpDX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,4BAA4BA,CAAA,EAAG;EAC7C,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}