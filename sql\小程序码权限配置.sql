-- 小程序码功能权限配置SQL脚本
-- 执行日期: 2025-01-17
-- 功能描述: 为活动报名管理添加小程序码生成权限

-- 1. 添加活动小程序码权限菜单
INSERT INTO sys_menu (
    menu_id, 
    menu_name, 
    parent_id, 
    order_num, 
    path, 
    component, 
    query, 
    route_name, 
    is_frame, 
    is_cache, 
    menu_type, 
    visible, 
    status, 
    perms, 
    icon, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    remark
) VALUES (
    216581,                          -- 菜单ID
    '活动小程序码',                   -- 菜单名称
    21436,                           -- 父菜单ID (活动报名管理)
    6,                               -- 显示顺序
    '',                              -- 路由地址
    '',                              -- 组件路径
    '',                              -- 路由参数
    '',                              -- 路由名称
    1,                               -- 是否为外链 (0是 1否)
    0,                               -- 是否缓存 (0缓存 1不缓存)
    'F',                             -- 菜单类型 (M目录 C菜单 F按钮)
    '0',                             -- 菜单状态 (0显示 1隐藏)
    '0',                             -- 菜单状态 (0正常 1停用)
    'miniapp:event:qrcode',          -- 权限标识
    '#',                             -- 菜单图标
    'admin',                         -- 创建者
    NOW(),                           -- 创建时间
    '',                              -- 更新者
    NULL,                            -- 更新时间
    '生成活动小程序码权限'            -- 备注
);

-- 2. 为超级管理员角色分配新权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, 216581);

-- 3. 验证权限配置
-- 查看新添加的权限菜单
SELECT menu_id, menu_name, parent_id, perms, menu_type, status 
FROM sys_menu 
WHERE menu_id = 216581;

-- 查看活动报名管理下的所有权限
SELECT menu_id, menu_name, parent_id, order_num, perms, menu_type 
FROM sys_menu 
WHERE parent_id = 21436 
ORDER BY order_num, menu_id;

-- 查看角色权限分配情况
SELECT rm.role_id, r.role_name, rm.menu_id, m.menu_name, m.perms 
FROM sys_role_menu rm 
JOIN sys_role r ON rm.role_id = r.role_id 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE rm.menu_id = 216581;

-- 权限说明:
-- miniapp:event:qrcode - 活动小程序码生成权限
-- 
-- 使用说明:
-- 1. 该权限允许用户调用以下接口:
--    - POST /miniapp/event/getQRCode (获取活动小程序码)
--    - POST /miniapp/event/getCustomQRCode (获取自定义小程序码)
-- 
-- 2. 需要为相关用户角色分配此权限才能使用小程序码功能
-- 
-- 3. 确保微信小程序配置正确 (appid 和 secret)
-- 
-- 4. 生成的小程序码永久有效，但有数量限制 (100,000个)

-- 如需为其他角色分配权限，可执行:
-- INSERT INTO sys_role_menu (role_id, menu_id) VALUES (角色ID, 216581);

-- 如需删除权限配置，可执行:
-- DELETE FROM sys_role_menu WHERE menu_id = 216581;
-- DELETE FROM sys_menu WHERE menu_id = 216581;
