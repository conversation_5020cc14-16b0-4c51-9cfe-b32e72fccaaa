# 小程序码接口文档

## 概述

本文档描述了活动报名管理系统中新增的小程序码生成接口。这些接口基于微信小程序官方API实现，用于生成活动详情页面的小程序码。

## 接口列表

### 1. 获取活动小程序码

**接口地址：** `POST /miniapp/event/getQRCode`

**接口描述：** 为指定活动生成小程序码，用户扫码后可直接进入活动详情页面。

**权限要求：** `miniapp:event:qrcode`

**请求参数：**
```json
{
  "eventId": 123
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| eventId | Long | 是 | 活动ID |

**响应示例：**
```json
{
  "code": 200,
  "msg": "小程序码生成成功",
  "data": {
    "eventId": 123,
    "eventTitle": "春节联欢活动",
    "qrcode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

### 2. 获取自定义小程序码

**接口地址：** `POST /miniapp/event/getCustomQRCode`

**接口描述：** 生成自定义参数的小程序码，支持所有微信API参数。

**权限要求：** `miniapp:event:qrcode`

**请求参数：**
```json
{
  "path": "pages/event/detail?eventId=123",
  "width": 430,
  "autoColor": false,
  "lineColor": {
    "r": 0,
    "g": 0,
    "b": 0
  },
  "isHyaline": false,
  "envVersion": "release"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| path | String | 是 | 扫码进入的小程序页面路径，最大长度1024个字符 |
| width | Integer | 否 | 二维码宽度，单位px，默认430，最小280，最大1280 |
| autoColor | Boolean | 否 | 是否自动配置线条颜色，默认false |
| lineColor | Object | 否 | 线条颜色RGB值，autoColor为false时生效 |
| isHyaline | Boolean | 否 | 是否需要透明底色，默认false |
| envVersion | String | 否 | 小程序版本：release(正式版)、trial(体验版)、develop(开发版)，默认release |

**响应示例：**
```json
{
  "code": 200,
  "msg": "小程序码生成成功",
  "data": {
    "path": "pages/event/detail?eventId=123",
    "qrcode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误（如活动不存在、路径为空等） |
| 500 | 服务器内部错误（如微信API调用失败） |

## 使用说明

1. **权限配置**：需要为用户分配 `miniapp:event:qrcode` 权限才能调用这些接口。

2. **微信配置**：确保在 `application.yml` 中正确配置了微信小程序的 `appid` 和 `secret`。

3. **图片格式**：返回的 `qrcode` 字段是完整的 Data URL 格式，可以直接用于 HTML 的 `<img>` 标签或前端显示。

4. **限制说明**：
   - 与 createQRCode 总共生成的码数量限制为 100,000
   - 小程序码永久有效
   - 建议合理使用，避免频繁调用

## 前端使用示例

```javascript
// 获取活动小程序码
async function getEventQRCode(eventId) {
  try {
    const response = await fetch('/miniapp/event/getQRCode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({ eventId: eventId })
    });
    
    const result = await response.json();
    if (result.code === 200) {
      // 显示二维码
      document.getElementById('qrcode-img').src = result.data.qrcode;
    } else {
      console.error('生成失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}
```

## 注意事项

1. 生成的小程序码包含活动ID参数，用户扫码后会直接跳转到对应活动的详情页面。
2. 请确保小程序中有对应的页面路径来处理扫码跳转。
3. 建议在生成小程序码前先验证活动是否存在且状态正常。
4. 生成的图片为PNG格式，适合在各种场景下使用。
