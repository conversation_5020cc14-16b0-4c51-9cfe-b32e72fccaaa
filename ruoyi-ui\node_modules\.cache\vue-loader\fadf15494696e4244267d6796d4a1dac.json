{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue", "mtime": 1754298434316}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVtYW5kQ2F0ZWdvcnksIGdldERlbWFuZENhdGVnb3J5LCBkZWxEZW1hbmRDYXRlZ29yeSwgYWRkRGVtYW5kQ2F0ZWdvcnksIHVwZGF0ZURlbWFuZENhdGVnb3J5IH0gZnJvbSAiQC9hcGkvbWluaWFwcC9kZW1hbmRjYXRlZ29yeSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk1pbmlEZW1hbmRDYXRlZ29yeSIsDQogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6ZyA5rGC57G75Z6L6KGo5qC85pWw5o2uDQogICAgICBjYXRlZ29yeUxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGNhdGVnb3J5TmFtZTogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY2F0ZWdvcnlOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuexu+Wei+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGNhdGVnb3J5Q29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnsbvlnovmoIfor4bkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHBhdHRlcm46IC9eW2EtekEtWl1bYS16QS1aMC05X10qJC8sIG1lc3NhZ2U6ICLnsbvlnovmoIfor4blv4Xpobvku6XlrZfmr43lvIDlpLTvvIzlj6rog73ljIXlkKvlrZfmr43jgIHmlbDlrZflkozkuIvliJLnur8iLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBzdGF0dXM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi54q25oCB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V6YWN572u55u45YWzDQogICAgICBmb3JtQ29uZmlnT3BlbjogZmFsc2UsDQogICAgICBwcmV2aWV3T3BlbjogZmFsc2UsDQogICAgICBjdXJyZW50Q2F0ZWdvcnlJZDogbnVsbCwNCiAgICAgIGZvcm1GaWVsZHNMaXN0OiBbXSwgLy8g5L+d55WZ5YW85a655oCnDQogICAgICBmb3JtTW9kdWxlc0xpc3Q6IFtdLCAvLyDmlrDnmoTmqKHlnZfljJbnu5PmnoQNCiAgICAgIHNlbGVjdGVkVGVtcGxhdGU6ICcnLA0KICAgICAgcHJldmlld0RhdGE6IHt9LCAvLyDpooTop4jljLrln5/nmoTooajljZXmlbDmja4NCiAgICAgIHByZXZpZXdEaWFsb2dEYXRhOiB7fSwgLy8g6aKE6KeI5a+56K+d5qGG55qE6KGo5Y2V5pWw5o2uDQogICAgICAvLyDlm77moIfnm7jlhbMNCiAgICAgIGljb25VcGxvYWRlck9wZW46IGZhbHNlLA0KICAgICAgY3VycmVudE1vZHVsZUluZGV4OiAtMSwNCiAgICAgIHVwbG9hZGVkSWNvbjogJycNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQoNCiAgICAvKiog5p+l6K+i6ZyA5rGC57G75Z6L5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0RGVtYW5kQ2F0ZWdvcnkodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2F0ZWdvcnlMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCg0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGNhdGVnb3J5SWQ6IG51bGwsDQogICAgICAgIGNhdGVnb3J5TmFtZTogbnVsbCwNCiAgICAgICAgY2F0ZWdvcnlDb2RlOiBudWxsLA0KICAgICAgICBjYXRlZ29yeVNob3J0TmFtZTogbnVsbCwNCiAgICAgICAgY2F0ZWdvcnlJY29uOiBudWxsLA0KICAgICAgICBjYXRlZ29yeURlc2M6IG51bGwsDQogICAgICAgIHNvcnRPcmRlcjogMCwNCiAgICAgICAgc3RhdHVzOiAiMCIsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY2F0ZWdvcnlJZCk7DQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6ZyA5rGC57G75Z6LIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBjYXRlZ29yeUlkID0gcm93LmNhdGVnb3J5SWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXREZW1hbmRDYXRlZ29yeShjYXRlZ29yeUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnpnIDmsYLnsbvlnosiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uY2F0ZWdvcnlJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVEZW1hbmRDYXRlZ29yeSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERlbWFuZENhdGVnb3J5KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3J5SWRzID0gcm93LmNhdGVnb3J5SWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpnIDmsYLnsbvlnovnvJblj7fkuLoiJyArIGNhdGVnb3J5SWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsRGVtYW5kQ2F0ZWdvcnkoY2F0ZWdvcnlJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdtaW5pYXBwL2RlbWFuZGNhdGVnb3J5L2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYOmcgOaxguexu+Wei+aVsOaNrl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8qKiDmjpLluo/kv67mlLkgKi8NCiAgICBoYW5kbGVTb3J0Q2hhbmdlKHJvdykgew0KICAgICAgdXBkYXRlRGVtYW5kQ2F0ZWdvcnkocm93KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5o6S5bqP5L+u5pS55oiQ5YqfIik7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDooajljZXphY3nva7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVGb3JtQ29uZmlnKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Q2F0ZWdvcnlJZCA9IHJvdy5jYXRlZ29yeUlkOw0KICAgICAgdGhpcy5mb3JtRmllbGRzTGlzdCA9IFtdOw0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbXTsNCg0KICAgICAgLy8g5aaC5p6c5bey5pyJ6YWN572u77yM6Kej5p6QSlNPTg0KICAgICAgaWYgKHJvdy5mb3JtRmllbGRzKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcGFyc2VkRGF0YSA9IEpTT04ucGFyc2Uocm93LmZvcm1GaWVsZHMpOw0KDQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5Li65paw55qE5qih5Z2X5YyW57uT5p6EDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkRGF0YSkgJiYgcGFyc2VkRGF0YS5sZW5ndGggPiAwICYmIHBhcnNlZERhdGFbMF0uaGFzT3duUHJvcGVydHkoJ25hbWUnKSkgew0KICAgICAgICAgICAgLy8g5paw55qE5qih5Z2X5YyW57uT5p6EDQogICAgICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdCA9IHBhcnNlZERhdGE7DQogICAgICAgICAgICAvLyDnoa7kv53miYDmnInlrZfmrrXpg73mnIkgaGlkZGVuIOWxnuaApw0KICAgICAgICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QuZm9yRWFjaChtb2R1bGUgPT4gew0KICAgICAgICAgICAgICBpZiAobW9kdWxlLmZpZWxkcykgew0KICAgICAgICAgICAgICAgIG1vZHVsZS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgICBpZiAoZmllbGQuaGlkZGVuID09PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kc2V0KGZpZWxkLCAnaGlkZGVuJywgZmFsc2UpOw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkRGF0YSkpIHsNCiAgICAgICAgICAgIC8vIOaXp+eahOWtl+auteWIl+ihqOe7k+aehO+8jOi9rOaNouS4uuaooeWdl+WMlue7k+aehA0KICAgICAgICAgICAgdGhpcy5mb3JtRmllbGRzTGlzdCA9IHBhcnNlZERhdGE7DQogICAgICAgICAgICBpZiAocGFyc2VkRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIC8vIOS4uuaXp+Wtl+autea3u+WKoCBoaWRkZW4g5bGe5oCnDQogICAgICAgICAgICAgIHBhcnNlZERhdGEuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGZpZWxkLmhpZGRlbiA9PT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRzZXQoZmllbGQsICdoaWRkZW4nLCBmYWxzZSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbew0KICAgICAgICAgICAgICAgIG5hbWU6ICfln7rnoYDkv6Hmga8nLA0KICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnJywNCiAgICAgICAgICAgICAgICBmaWVsZHM6IHBhcnNlZERhdGENCiAgICAgICAgICAgICAgfV07DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6KGo5Y2V6YWN572u5aSx6LSlOicsIGUpOw0KICAgICAgICAgIHRoaXMuZm9ybUZpZWxkc0xpc3QgPSBbXTsNCiAgICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5pbml0UHJldmlld0RhdGEoKTsNCiAgICB9LA0KDQogICAgLyoqIOWIneWni+WMlumihOiniOaVsOaNriAqLw0KICAgIGluaXRQcmV2aWV3RGF0YSgpIHsNCiAgICAgIHRoaXMucHJldmlld0RhdGEgPSB7fTsNCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0LmZvckVhY2gobW9kdWxlID0+IHsNCiAgICAgICAgaWYgKG1vZHVsZS5maWVsZHMpIHsNCiAgICAgICAgICBtb2R1bGUuZmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgICAgaWYgKGZpZWxkLm5hbWUpIHsNCiAgICAgICAgICAgICAgaWYgKGZpZWxkLnR5cGUgPT09ICdjaGVja2JveCcpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnByZXZpZXdEYXRhW2ZpZWxkLm5hbWVdID0gW107DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ251bWJlcicpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnByZXZpZXdEYXRhW2ZpZWxkLm5hbWVdID0gbnVsbDsNCiAgICAgICAgICAgICAgfSBlbHNlIGlmIChmaWVsZC50eXBlID09PSAnZGF0ZScgfHwgZmllbGQudHlwZSA9PT0gJ3RpbWUnKSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGF0YVtmaWVsZC5uYW1lXSA9IG51bGw7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGF0YVtmaWVsZC5uYW1lXSA9ICcnOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5re75Yqg6KGo5Y2V5qih5Z2XICovDQogICAgYWRkRm9ybU1vZHVsZSgpIHsNCiAgICAgIGNvbnN0IG5ld01vZHVsZSA9IHsNCiAgICAgICAgbmFtZTogJ+aWsOaooeWdlycsDQogICAgICAgIGRlc2NyaXB0aW9uOiAnJywNCiAgICAgICAgZmllbGRzOiBbXSwNCiAgICAgICAgaWNvbjogJycgLy8g6buY6K6k5peg5Zu+5qCH77yM6ZyA6KaB55So5oi35LiK5LygDQogICAgICB9Ow0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QucHVzaChuZXdNb2R1bGUpOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5qih5Z2XICovDQogICAgcmVtb3ZlTW9kdWxlKG1vZHVsZUluZGV4KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTliKDpmaTor6XmqKHlnZflj4rlhbbmiYDmnInlrZfmrrXlkJfvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdC5zcGxpY2UobW9kdWxlSW5kZXgsIDEpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQoNCiAgICAvKiog5qih5Z2X5LiK56e7ICovDQogICAgbW92ZU1vZHVsZVVwKG1vZHVsZUluZGV4KSB7DQogICAgICBpZiAobW9kdWxlSW5kZXggPiAwKSB7DQogICAgICAgIGNvbnN0IHRlbXAgPSB0aGlzLmZvcm1Nb2R1bGVzTGlzdFttb2R1bGVJbmRleF07DQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1Nb2R1bGVzTGlzdCwgbW9kdWxlSW5kZXgsIHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4IC0gMV0pOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtTW9kdWxlc0xpc3QsIG1vZHVsZUluZGV4IC0gMSwgdGVtcCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmqKHlnZfkuIvnp7sgKi8NCiAgICBtb3ZlTW9kdWxlRG93bihtb2R1bGVJbmRleCkgew0KICAgICAgaWYgKG1vZHVsZUluZGV4IDwgdGhpcy5mb3JtTW9kdWxlc0xpc3QubGVuZ3RoIC0gMSkgew0KICAgICAgICBjb25zdCB0ZW1wID0gdGhpcy5mb3JtTW9kdWxlc0xpc3RbbW9kdWxlSW5kZXhdOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtTW9kdWxlc0xpc3QsIG1vZHVsZUluZGV4LCB0aGlzLmZvcm1Nb2R1bGVzTGlzdFttb2R1bGVJbmRleCArIDFdKTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybU1vZHVsZXNMaXN0LCBtb2R1bGVJbmRleCArIDEsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog6aqM6K+B5qih5Z2X5ZCN56ewICovDQogICAgdmFsaWRhdGVNb2R1bGVOYW1lKG1vZHVsZSkgew0KICAgICAgaWYgKCFtb2R1bGUubmFtZSB8fCBtb2R1bGUubmFtZS50cmltKCkgPT09ICcnKSB7DQogICAgICAgIG1vZHVsZS5uYW1lID0gJ+acquWRveWQjeaooeWdlyc7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlkJHmqKHlnZfmt7vliqDlrZfmrrUgKi8NCiAgICBhZGRGaWVsZFRvTW9kdWxlKG1vZHVsZUluZGV4KSB7DQogICAgICBjb25zdCBuZXdGaWVsZCA9IHsNCiAgICAgICAgbGFiZWw6ICcnLA0KICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgdHlwZTogJ2lucHV0JywNCiAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgICBoaWRkZW46IGZhbHNlLCAvLyDpu5jorqTkuI3pmpDol48NCiAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWlJywNCiAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgIH07DQogICAgICBpZiAoIXRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XS5maWVsZHMpIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XSwgJ2ZpZWxkcycsIFtdKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XS5maWVsZHMucHVzaChuZXdGaWVsZCk7DQogICAgICAvLyDmm7TmlrDpooTop4jmlbDmja4NCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0UHJldmlld0RhdGEoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5Zu+5qCH5ZG95LukICovDQogICAgaGFuZGxlSWNvbkNvbW1hbmQoY29tbWFuZCwgbW9kdWxlSW5kZXgpIHsNCiAgICAgIHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID0gbW9kdWxlSW5kZXg7DQoNCiAgICAgIGlmIChjb21tYW5kID09PSAndXBsb2FkJykgew0KICAgICAgICB0aGlzLnVwbG9hZGVkSWNvbiA9ICcnOw0KICAgICAgICB0aGlzLmljb25VcGxvYWRlck9wZW4gPSB0cnVlOw0KICAgICAgfSBlbHNlIGlmIChjb21tYW5kID09PSAncmVtb3ZlJykgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtTW9kdWxlc0xpc3RbbW9kdWxlSW5kZXhdLCAnaWNvbicsICcnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOaJk+W8gOWbvuagh+S4iuS8oOWZqCAqLw0KICAgIG9wZW5JY29uVXBsb2FkZXIobW9kdWxlSW5kZXgpIHsNCiAgICAgIHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID0gbW9kdWxlSW5kZXg7DQogICAgICB0aGlzLnVwbG9hZGVkSWNvbiA9ICcnOw0KICAgICAgdGhpcy5pY29uVXBsb2FkZXJPcGVuID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOehruiupOWbvuagh+S4iuS8oCAqLw0KICAgIGNvbmZpcm1JY29uVXBsb2FkKCkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID49IDAgJiYgdGhpcy51cGxvYWRlZEljb24pIHsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybU1vZHVsZXNMaXN0W3RoaXMuY3VycmVudE1vZHVsZUluZGV4XSwgJ2ljb24nLCB0aGlzLnVwbG9hZGVkSWNvbik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Zu+5qCH5LiK5Lyg5oiQ5YqfJyk7DQogICAgICB9DQogICAgICB0aGlzLmljb25VcGxvYWRlck9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuY3VycmVudE1vZHVsZUluZGV4ID0gLTE7DQogICAgICB0aGlzLnVwbG9hZGVkSWNvbiA9ICcnOw0KICAgIH0sDQoNCiAgICAvKiog5Y+W5raI5Zu+5qCH5LiK5LygICovDQogICAgY2FuY2VsSWNvblVwbG9hZCgpIHsNCiAgICAgIHRoaXMuaWNvblVwbG9hZGVyT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5jdXJyZW50TW9kdWxlSW5kZXggPSAtMTsNCiAgICAgIHRoaXMudXBsb2FkZWRJY29uID0gJyc7DQogICAgfSwNCg0KICAgIC8qKiDlrZfmrrXnsbvlnovlj5jljJbml7bnmoTlpITnkIYgKi8NCiAgICBvbkZpZWxkVHlwZUNoYW5nZShmaWVsZCkgew0KICAgICAgLy8g5qC55o2u5a2X5q6157G75Z6L6K6+572u6buY6K6k55qEcGxhY2Vob2xkZXINCiAgICAgIGNvbnN0IHBsYWNlaG9sZGVyTWFwID0gew0KICAgICAgICAnaW5wdXQnOiAn6K+36L6T5YWlJywNCiAgICAgICAgJ3RleHRhcmVhJzogJ+ivt+i+k+WFpScsDQogICAgICAgICdudW1iZXInOiAn6K+36L6T5YWl5pWw5a2XJywNCiAgICAgICAgJ3RlbCc6ICfor7fovpPlhaXnlLXor53lj7fnoIEnLA0KICAgICAgICAnZW1haWwnOiAn6K+36L6T5YWl6YKu566x5Zyw5Z2AJywNCiAgICAgICAgJ3JhZGlvJzogJycsDQogICAgICAgICdjaGVja2JveCc6ICcnLA0KICAgICAgICAnc2VsZWN0JzogJ+ivt+mAieaLqScsDQogICAgICAgICdkYXRlJzogJ+ivt+mAieaLqeaXpeacnycsDQogICAgICAgICd0aW1lJzogJ+ivt+mAieaLqeaXtumXtCcsDQogICAgICAgICdmaWxlJzogJ+eCueWHu+S4iuS8oCcsDQogICAgICAgICdzdGF0aWMnOiAnJw0KICAgICAgfTsNCg0KICAgICAgaWYgKCFmaWVsZC5wbGFjZWhvbGRlciB8fCBmaWVsZC5wbGFjZWhvbGRlciA9PT0gJycpIHsNCiAgICAgICAgZmllbGQucGxhY2Vob2xkZXIgPSBwbGFjZWhvbGRlck1hcFtmaWVsZC50eXBlXSB8fCAn6K+36L6T5YWlJzsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5piv6Z2Z5oCB5bGV56S65a2X5q6177yM6K6+572u6buY6K6k5YaF5a655ZKM5riF6Zmk5b+F5aGr54q25oCBDQogICAgICBpZiAoZmllbGQudHlwZSA9PT0gJ3N0YXRpYycpIHsNCiAgICAgICAgZmllbGQucmVxdWlyZWQgPSBmYWxzZTsNCiAgICAgICAgaWYgKCFmaWVsZC5zdGF0aWNDb250ZW50KSB7DQogICAgICAgICAgZmllbGQuc3RhdGljQ29udGVudCA9ICfov5nph4zmmK/pnZnmgIHlsZXnpLrlhoXlrrknOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlrZfmrrXpmpDol4/nirbmgIHlj5jljJbml7bnmoTlpITnkIYgKi8NCiAgICBvbkZpZWxkSGlkZGVuQ2hhbmdlKCkgew0KICAgICAgLy8g6Kem5Y+R6KeG5Zu+5pu05paw77yM5peg6ZyA6aKd5aSW5aSE55CGDQogICAgfSwNCg0KICAgIC8qKiDku47mqKHlnZfliKDpmaTlrZfmrrUgKi8NCiAgICByZW1vdmVGaWVsZEZyb21Nb2R1bGUobW9kdWxlSW5kZXgsIGZpZWxkSW5kZXgpIHsNCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0W21vZHVsZUluZGV4XS5maWVsZHMuc3BsaWNlKGZpZWxkSW5kZXgsIDEpOw0KICAgIH0sDQoNCiAgICAvKiog5qih5Z2X5YaF5a2X5q615LiK56e7ICovDQogICAgbW92ZUZpZWxkVXBJbk1vZHVsZShtb2R1bGVJbmRleCwgZmllbGRJbmRleCkgew0KICAgICAgY29uc3QgZmllbGRzID0gdGhpcy5mb3JtTW9kdWxlc0xpc3RbbW9kdWxlSW5kZXhdLmZpZWxkczsNCiAgICAgIGlmIChmaWVsZEluZGV4ID4gMCkgew0KICAgICAgICBjb25zdCB0ZW1wID0gZmllbGRzW2ZpZWxkSW5kZXhdOw0KICAgICAgICB0aGlzLiRzZXQoZmllbGRzLCBmaWVsZEluZGV4LCBmaWVsZHNbZmllbGRJbmRleCAtIDFdKTsNCiAgICAgICAgdGhpcy4kc2V0KGZpZWxkcywgZmllbGRJbmRleCAtIDEsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5qih5Z2X5YaF5a2X5q615LiL56e7ICovDQogICAgbW92ZUZpZWxkRG93bkluTW9kdWxlKG1vZHVsZUluZGV4LCBmaWVsZEluZGV4KSB7DQogICAgICBjb25zdCBmaWVsZHMgPSB0aGlzLmZvcm1Nb2R1bGVzTGlzdFttb2R1bGVJbmRleF0uZmllbGRzOw0KICAgICAgaWYgKGZpZWxkSW5kZXggPCBmaWVsZHMubGVuZ3RoIC0gMSkgew0KICAgICAgICBjb25zdCB0ZW1wID0gZmllbGRzW2ZpZWxkSW5kZXhdOw0KICAgICAgICB0aGlzLiRzZXQoZmllbGRzLCBmaWVsZEluZGV4LCBmaWVsZHNbZmllbGRJbmRleCArIDFdKTsNCiAgICAgICAgdGhpcy4kc2V0KGZpZWxkcywgZmllbGRJbmRleCArIDEsIHRlbXApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5re75Yqg6KGo5Y2V5a2X5q6177yI5YW85a655pen5pa55rOV77yJICovDQogICAgYWRkRm9ybUZpZWxkKCkgew0KICAgICAgLy8g5aaC5p6c5rKh5pyJ5qih5Z2X77yM5YWI5Yib5bu65LiA5Liq6buY6K6k5qih5Z2XDQogICAgICBpZiAodGhpcy5mb3JtTW9kdWxlc0xpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuYWRkRm9ybU1vZHVsZSgpOw0KICAgICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdFswXS5uYW1lID0gJ+WfuuehgOS/oeaBryc7DQogICAgICB9DQoNCiAgICAgIC8vIOa3u+WKoOWIsOesrOS4gOS4quaooeWdlw0KICAgICAgdGhpcy5hZGRGaWVsZFRvTW9kdWxlKDApOw0KICAgIH0sDQoNCg0KDQogICAgLyoqIOeUn+aIkOWtl+auteWQjeensCAqLw0KICAgIGdlbmVyYXRlRmllbGROYW1lKGZpZWxkKSB7DQogICAgICBpZiAoZmllbGQubGFiZWwpIHsNCiAgICAgICAgLy8g5omp5bGV55qE5Lit5paH6L2s6Iux5paH5pig5bCEDQogICAgICAgIGNvbnN0IG5hbWVNYXAgPSB7DQogICAgICAgICAgLy8g5Z+656GA5L+h5oGvDQogICAgICAgICAgJ+Wnk+WQjSc6ICduYW1lJywNCiAgICAgICAgICAn6IGU57O75Lq6JzogJ2NvbnRhY3RfbmFtZScsDQogICAgICAgICAgJ+iBlOezu+eUteivnSc6ICdwaG9uZScsDQogICAgICAgICAgJ+aJi+acuuWPtyc6ICdwaG9uZScsDQogICAgICAgICAgJ+eUteivnSc6ICdwaG9uZScsDQogICAgICAgICAgJ+mCrueusSc6ICdlbWFpbCcsDQogICAgICAgICAgJ+mCrueuseWcsOWdgCc6ICdlbWFpbCcsDQogICAgICAgICAgJ+WFrOWPuCc6ICdjb21wYW55JywNCiAgICAgICAgICAn5YWs5Y+45ZCN56ewJzogJ2NvbXBhbnlfbmFtZScsDQogICAgICAgICAgJ+iBjOS9jSc6ICdwb3NpdGlvbicsDQogICAgICAgICAgJ+mDqOmXqCc6ICdkZXBhcnRtZW50JywNCiAgICAgICAgICAn5Zyw5Z2AJzogJ2FkZHJlc3MnLA0KICAgICAgICAgICfor6bnu4blnLDlnYAnOiAnZGV0YWlsZWRfYWRkcmVzcycsDQoNCiAgICAgICAgICAvLyDmioDmnK/nm7jlhbMNCiAgICAgICAgICAn5oqA5pyv5pa55ZCRJzogJ3RlY2hfZGlyZWN0aW9uJywNCiAgICAgICAgICAn5oqA5pyv5qCIJzogJ3RlY2hfc3RhY2snLA0KICAgICAgICAgICflvIDlj5Hor63oqIAnOiAncHJvZ3JhbW1pbmdfbGFuZ3VhZ2UnLA0KICAgICAgICAgICfpobnnm67lkajmnJ8nOiAncHJvamVjdF9kdXJhdGlvbicsDQogICAgICAgICAgJ+mihOeul+iMg+WbtCc6ICdidWRnZXRfcmFuZ2UnLA0KICAgICAgICAgICfpobnnm67or6bnu4bpnIDmsYInOiAnZGV0YWlsZWRfcmVxdWlyZW1lbnRzJywNCiAgICAgICAgICAn5oqA5pyv6KaB5rGCJzogJ3RlY2hfcmVxdWlyZW1lbnRzJywNCg0KICAgICAgICAgIC8vIOW4guWcuuaOqOW5v+ebuOWFsw0KICAgICAgICAgICfmjqjlub/nsbvlnosnOiAncHJvbW90aW9uX3R5cGUnLA0KICAgICAgICAgICfnm67moIflrqLmiLfnvqTkvZMnOiAndGFyZ2V0X2F1ZGllbmNlJywNCiAgICAgICAgICAn5o6o5bm/5rig6YGTJzogJ3Byb21vdGlvbl9jaGFubmVscycsDQogICAgICAgICAgJ+aOqOW5v+mihOeulyc6ICdwcm9tb3Rpb25fYnVkZ2V0JywNCiAgICAgICAgICAn5o6o5bm/5pe26Ze0JzogJ3Byb21vdGlvbl9kdXJhdGlvbicsDQogICAgICAgICAgJ+aOqOW5v+ebruaghyc6ICdwcm9tb3Rpb25fZ29hbHMnLA0KDQogICAgICAgICAgLy8g5oub6IGY55u45YWzDQogICAgICAgICAgJ+aLm+iBmOiBjOS9jSc6ICdqb2JfcG9zaXRpb24nLA0KICAgICAgICAgICflt6XkvZznu4/pqownOiAnd29ya19leHBlcmllbmNlJywNCiAgICAgICAgICAn5a2m5Y6G6KaB5rGCJzogJ2VkdWNhdGlvbl9yZXF1aXJlbWVudCcsDQogICAgICAgICAgJ+iWqui1hOiMg+WbtCc6ICdzYWxhcnlfcmFuZ2UnLA0KICAgICAgICAgICflt6XkvZzlnLDngrknOiAnd29ya19sb2NhdGlvbicsDQogICAgICAgICAgJ+iBjOS9jeaPj+i/sCc6ICdqb2JfZGVzY3JpcHRpb24nLA0KDQogICAgICAgICAgLy8g5oqV6LWE55u45YWzDQogICAgICAgICAgJ+aKlei1hOexu+Weiyc6ICdpbnZlc3RtZW50X3R5cGUnLA0KICAgICAgICAgICfmipXotYTph5Hpop0nOiAnaW52ZXN0bWVudF9hbW91bnQnLA0KICAgICAgICAgICfmipXotYTpmLbmrrUnOiAnaW52ZXN0bWVudF9zdGFnZScsDQogICAgICAgICAgJ+ihjOS4mumihuWfnyc6ICdpbmR1c3RyeV9maWVsZCcsDQogICAgICAgICAgJ+mhueebruS7i+e7jSc6ICdwcm9qZWN0X2ludHJvZHVjdGlvbicsDQoNCiAgICAgICAgICAvLyDph4fotK3nm7jlhbMNCiAgICAgICAgICAn5Lqn5ZOB5ZCN56ewJzogJ3Byb2R1Y3RfbmFtZScsDQogICAgICAgICAgJ+mHh+i0reaVsOmHjyc6ICdwdXJjaGFzZV9xdWFudGl0eScsDQogICAgICAgICAgJ+i0qOmHj+imgeaxgic6ICdxdWFsaXR5X3JlcXVpcmVtZW50cycsDQogICAgICAgICAgJ+S6pOS7mOaXtumXtCc6ICdkZWxpdmVyeV90aW1lJywNCiAgICAgICAgICAn6YeH6LSt6aKE566XJzogJ3B1cmNoYXNlX2J1ZGdldCcsDQoNCiAgICAgICAgICAvLyDpgJrnlKjlrZfmrrUNCiAgICAgICAgICAn6ZyA5rGC5o+P6L+wJzogJ2Rlc2NyaXB0aW9uJywNCiAgICAgICAgICAn6K+m57uG6K+05piOJzogJ2RldGFpbGVkX2Rlc2NyaXB0aW9uJywNCiAgICAgICAgICAn5aSH5rOoJzogJ3JlbWFyaycsDQogICAgICAgICAgJ+ivtOaYjic6ICdub3RlJywNCiAgICAgICAgICAn5qCH6aKYJzogJ3RpdGxlJywNCiAgICAgICAgICAn5YaF5a65JzogJ2NvbnRlbnQnLA0KICAgICAgICAgICfml7bpl7QnOiAndGltZScsDQogICAgICAgICAgJ+aXpeacnyc6ICdkYXRlJywNCiAgICAgICAgICAn5paH5Lu2JzogJ2ZpbGUnLA0KICAgICAgICAgICflm77niYcnOiAnaW1hZ2UnLA0KICAgICAgICAgICfpmYTku7YnOiAnYXR0YWNobWVudCcNCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDlpoLmnpzmnInnm7TmjqXmmKDlsITvvIzkvb/nlKjmmKDlsITlgLwNCiAgICAgICAgaWYgKG5hbWVNYXBbZmllbGQubGFiZWxdKSB7DQogICAgICAgICAgZmllbGQubmFtZSA9IG5hbWVNYXBbZmllbGQubGFiZWxdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWQpuWImei/m+ihjOaZuuiDvei9rOaNog0KICAgICAgICAgIGxldCBuYW1lID0gZmllbGQubGFiZWwNCiAgICAgICAgICAgIC5yZXBsYWNlKC9bXHNcLVwvXFxdL2csICdfJykgLy8g5pu/5o2i56m65qC844CB5qiq57q/44CB5pac57q/5Li65LiL5YiS57q/DQogICAgICAgICAgICAucmVwbGFjZSgvW15cd1x1NGUwMC1cdTlmYTVdL2csICcnKSAvLyDnp7vpmaTnibnmrorlrZfnrKbvvIzkv53nlZnkuK3oi7HmloflkozmlbDlrZcNCiAgICAgICAgICAgIC50b0xvd2VyQ2FzZSgpOw0KDQogICAgICAgICAgLy8g5aaC5p6c5YyF5ZCr5Lit5paH77yM5bCd6K+V6L2s5o2i5Li65ou86Z+z5oiW6Iux5paHDQogICAgICAgICAgaWYgKC9bXHU0ZTAwLVx1OWZhNV0vLnRlc3QobmFtZSkpIHsNCiAgICAgICAgICAgIC8vIOeugOWNleeahOS4reaWh+WFs+mUruivjeabv+aNog0KICAgICAgICAgICAgbmFtZSA9IG5hbWUNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+exu+Weiy9nLCAndHlwZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/lkI3np7AvZywgJ25hbWUnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv5pe26Ze0L2csICd0aW1lJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+aXpeacny9nLCAnZGF0ZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/lnLDlnYAvZywgJ2FkZHJlc3MnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv55S16K+dL2csICdwaG9uZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/pgq7nrrEvZywgJ2VtYWlsJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+WFrOWPuC9nLCAnY29tcGFueScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/mj4/ov7AvZywgJ2Rlc2NyaXB0aW9uJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+ivtOaYji9nLCAnbm90ZScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/lpIfms6gvZywgJ3JlbWFyaycpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/opoHmsYIvZywgJ3JlcXVpcmVtZW50JykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+iMg+WbtC9nLCAncmFuZ2UnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv6aKE566XL2csICdidWRnZXQnKQ0KICAgICAgICAgICAgICAucmVwbGFjZSgv5pWw6YePL2csICdxdWFudGl0eScpDQogICAgICAgICAgICAgIC5yZXBsYWNlKC/ku7fmoLwvZywgJ3ByaWNlJykNCiAgICAgICAgICAgICAgLnJlcGxhY2UoL+i0ueeUqC9nLCAnY29zdCcpOw0KDQogICAgICAgICAgICAvLyDlpoLmnpzov5jmnInkuK3mlofvvIzkvb/nlKjmi7zpn7PpppblrZfmr43miJbkv53mjIHljp/moLcNCiAgICAgICAgICAgIGlmICgvW1x1NGUwMC1cdTlmYTVdLy50ZXN0KG5hbWUpKSB7DQogICAgICAgICAgICAgIG5hbWUgPSAnZmllbGRfJyArIERhdGUubm93KCkudG9TdHJpbmcoKS5zbGljZSgtNik7IC8vIOS9v+eUqOaXtumXtOaIs+WQjjbkvY3kvZzkuLrllK/kuIDmoIfor4YNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICBmaWVsZC5uYW1lID0gbmFtZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5bqU55So6aKE6K6+5qih5p2/ICovDQogICAgYXBwbHlUZW1wbGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkVGVtcGxhdGUgPT09ICdiYXNpYycpIHsNCiAgICAgICAgdGhpcy5hcHBseUJhc2ljVGVtcGxhdGUoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5zZWxlY3RlZFRlbXBsYXRlID09PSAndGVjaCcpIHsNCiAgICAgICAgdGhpcy5hcHBseVRlY2hUZW1wbGF0ZSgpOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnNlbGVjdGVkVGVtcGxhdGUgPT09ICdidXNpbmVzcycpIHsNCiAgICAgICAgdGhpcy5hcHBseUJ1c2luZXNzVGVtcGxhdGUoKTsNCiAgICAgIH0NCiAgICAgIC8vIOW6lOeUqOaooeadv+WQjuWIneWni+WMlumihOiniOaVsOaNrg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmluaXRQcmV2aWV3RGF0YSgpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlupTnlKjln7rnoYDpnIDmsYLmqKHmnb8gKi8NCiAgICBhcHBseUJhc2ljVGVtcGxhdGUoKSB7DQogICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdCA9IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfloavlhpnor7TmmI4nLA0KICAgICAgICAgIGRlc2NyaXB0aW9uOiAn6K+35LuU57uG6ZiF6K+75Lul5LiL6K+05piO5ZCO5aGr5YaZ6KGo5Y2VJywNCiAgICAgICAgICBmaWVsZHM6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfmuKnppqjmj5DnpLonLA0KICAgICAgICAgICAgICBuYW1lOiAndGlwcycsDQogICAgICAgICAgICAgIHR5cGU6ICdzdGF0aWMnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogZmFsc2UsDQogICAgICAgICAgICAgIGhpZGRlbjogZmFsc2UsDQogICAgICAgICAgICAgIG9wdGlvbnM6ICcnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICfor7flpoLlrp7loavlhpnku6XkuIvkv6Hmga/vvIzmiJHku6zlsIblnKgyNOWwj+aXtuWGheS4juaCqOWPluW+l+iBlOezu+OAguW4pirlj7fnmoTkuLrlv4XloavpobnjgIInDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WfuuehgOS/oeaBrycsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfor7floavlhpnpnIDmsYLnmoTln7rmnKzkv6Hmga8nLA0KICAgICAgICAgIGZpZWxkczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+mcgOaxguagh+mimCcsDQogICAgICAgICAgICAgIG5hbWU6ICd0aXRsZScsDQogICAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXpnIDmsYLmoIfpopgnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfpnIDmsYLmj4/ov7AnLA0KICAgICAgICAgICAgICBuYW1lOiAnZGVzY3JpcHRpb24nLA0KICAgICAgICAgICAgICB0eXBlOiAndGV4dGFyZWEnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36K+m57uG5o+P6L+w5oKo55qE6ZyA5rGCJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6IGU57O75pa55byPJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmeaCqOeahOiBlOezu+aWueW8j++8jOS7peS+v+aIkeS7rOS4juaCqOWPluW+l+iBlOezuycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6IGU57O75Lq6JywNCiAgICAgICAgICAgICAgbmFtZTogJ2NvbnRhY3RfbmFtZScsDQogICAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXogZTns7vkurrlp5PlkI0nLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfogZTns7vnlLXor50nLA0KICAgICAgICAgICAgICBuYW1lOiAncGhvbmUnLA0KICAgICAgICAgICAgICB0eXBlOiAndGVsJywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgIGhpZGRlbjogZmFsc2UsDQogICAgICAgICAgICAgIG9wdGlvbnM6ICcnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeaJi+acuuWPt+eggScsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9DQogICAgICBdOw0KDQogICAgICAvLyDnoa7kv53lrZfmrrXlkI3np7DmraPnoa7nlJ/miJANCiAgICAgIHRoaXMuZm9ybU1vZHVsZXNMaXN0LmZvckVhY2gobW9kdWxlID0+IHsNCiAgICAgICAgbW9kdWxlLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICB0aGlzLmdlbmVyYXRlRmllbGROYW1lKGZpZWxkKTsNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOW6lOeUqOaKgOacr+mcgOaxguaooeadvyAqLw0KICAgIGFwcGx5VGVjaFRlbXBsYXRlKCkgew0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn5oqA5pyv6ZyA5rGCJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+ivpue7huaPj+i/sOaCqOeahOaKgOacr+mcgOaxgicsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn5oqA5pyv5pa55ZCRJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJ+WJjeerr+W8gOWPkSzlkI7nq6/lvIDlj5Es56e75Yqo5byA5Y+RLOS6uuW3peaZuuiDvSzlpKfmlbDmja4s5LqR6K6h566XJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fpgInmi6nmioDmnK/mlrnlkJEnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfmioDmnK/moIgnLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ2NoZWNrYm94JywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnSmF2YSxQeXRob24sSmF2YVNjcmlwdCxSZWFjdCxWdWUsU3ByaW5nIEJvb3QsTXlTUUwsUmVkaXMnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+mhueebruivpue7humcgOaxgicsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAndGV4dGFyZWEnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36K+m57uG5o+P6L+w6aG555uu6ZyA5rGC44CB5Yqf6IO96KaB5rGC44CB5oqA5pyv6KaB5rGC562JJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6aG555uu5L+h5oGvJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmemhueebrueahOWfuuacrOS/oeaBrycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6aG555uu5ZGo5pyfJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJzHlkajlhoUsMS0y5ZGoLDItNOWRqCwxLTLkuKrmnIgsMi0z5Liq5pyILDPkuKrmnIjku6XkuIonLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqemhueebruWRqOacnycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+mihOeul+iMg+WbtCcsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAncmFkaW8nLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJzHkuIfku6XkuIssMS015LiHLDUtMTDkuIcsMTAtMjDkuIcsMjDkuIfku6XkuIonLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJycsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+iBlOezu+aWueW8jycsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfor7floavlhpnmgqjnmoTogZTns7vmlrnlvI8nLA0KICAgICAgICAgIGZpZWxkczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+iBlOezu+S6uicsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAnaW5wdXQnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6IGU57O75Lq65aeT5ZCNJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6IGU57O755S16K+dJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICd0ZWwnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5omL5py65Y+356CBJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0NCiAgICAgIF07DQoNCiAgICAgIC8vIOiHquWKqOeUn+aIkOWtl+auteWQjeensA0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QuZm9yRWFjaChtb2R1bGUgPT4gew0KICAgICAgICBtb2R1bGUuZmllbGRzLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgIHRoaXMuZ2VuZXJhdGVGaWVsZE5hbWUoZmllbGQpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5bqU55So5ZWG5Yqh5ZCI5L2c5qih5p2/ICovDQogICAgYXBwbHlCdXNpbmVzc1RlbXBsYXRlKCkgew0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn5ZCI5L2c5L+h5oGvJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmeWQiOS9nOebuOWFs+S/oeaBrycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn5ZCI5L2c57G75Z6LJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdyYWRpbycsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAn5oiY55Wl5ZCI5L2cLOaKgOacr+WQiOS9nCzluILlnLrlkIjkvZws5oqV6LWE5ZCI5L2cJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICcnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICflkIjkvZzmj4/ov7AnLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ3RleHRhcmVhJywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICAgIGhpZGRlbjogZmFsc2UsDQogICAgICAgICAgICAgIG9wdGlvbnM6ICcnLA0KICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogJ+ivt+ivpue7huaPj+i/sOWQiOS9nOWGheWuueOAgeWQiOS9nOaWueW8j+OAgeacn+acm+i+vuaIkOeahOebruagh+etiScsDQogICAgICAgICAgICAgIHN0YXRpY0NvbnRlbnQ6ICcnDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+WFrOWPuOS/oeaBrycsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfor7floavlhpnmgqjnmoTlhazlj7jln7rmnKzkv6Hmga8nLA0KICAgICAgICAgIGZpZWxkczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBsYWJlbDogJ+WFrOWPuOWQjeensCcsDQogICAgICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgICAgICB0eXBlOiAnaW5wdXQnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJycsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5YWs5Y+45YWo56ewJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn5YWs5Y+46KeE5qihJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJzEw5Lq65Lul5LiLLDEwLTUw5Lq6LDUwLTIwMOS6uiwyMDAtNTAw5Lq6LDUwMOS6uuS7peS4iicsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oup5YWs5Y+46KeE5qihJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6KGM5Lia6aKG5Z+fJywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLA0KICAgICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgICAgaGlkZGVuOiBmYWxzZSwNCiAgICAgICAgICAgICAgb3B0aW9uczogJ+S6kuiBlOe9kSzph5Hono0s5pWZ6IKyLOWMu+eWlyzliLbpgKDkuJos5pyN5Yqh5LiaLOWFtuS7licsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAn6K+36YCJ5oup6KGM5Lia6aKG5Z+fJywNCiAgICAgICAgICAgICAgc3RhdGljQ29udGVudDogJycNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6IGU57O75pa55byPJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivt+Whq+WGmeiBlOezu+aWueW8j++8jOS7peS+v+aIkeS7rOS4juaCqOWPluW+l+iBlOezuycsDQogICAgICAgICAgZmllbGRzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxhYmVsOiAn6IGU57O75Lq6JywNCiAgICAgICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXogZTns7vkurrlp5PlkI0nLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfogZTns7vnlLXor50nLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ3RlbCcsDQogICAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXmiYvmnLrlj7fnoIEnLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGFiZWw6ICfpgq7nrrHlnLDlnYAnLA0KICAgICAgICAgICAgICBuYW1lOiAnJywNCiAgICAgICAgICAgICAgdHlwZTogJ2VtYWlsJywNCiAgICAgICAgICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgICAgICAgICBoaWRkZW46IGZhbHNlLA0KICAgICAgICAgICAgICBvcHRpb25zOiAnJywNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXpgq7nrrHlnLDlnYDvvIjpgInloavvvIknLA0KICAgICAgICAgICAgICBzdGF0aWNDb250ZW50OiAnJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgXTsNCg0KICAgICAgLy8g6Ieq5Yqo55Sf5oiQ5a2X5q615ZCN56ewDQogICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdC5mb3JFYWNoKG1vZHVsZSA9PiB7DQogICAgICAgIG1vZHVsZS5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgICAgdGhpcy5nZW5lcmF0ZUZpZWxkTmFtZShmaWVsZCk7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5blrZfmrrXpgInpobkgKi8NCiAgICBnZXRGaWVsZE9wdGlvbnMoZmllbGQpIHsNCiAgICAgIGlmICghZmllbGQub3B0aW9ucyB8fCBmaWVsZC5vcHRpb25zLnRyaW0oKSA9PT0gJycpIHJldHVybiBbXTsNCiAgICAgIHJldHVybiBmaWVsZC5vcHRpb25zLnNwbGl0KCcsJykubWFwKG9wdGlvbiA9PiBvcHRpb24udHJpbSgpKS5maWx0ZXIob3B0aW9uID0+IG9wdGlvbiAhPT0gJycpOw0KICAgIH0sDQoNCiAgICAvKiog6aKE6KeI6KGo5Y2VICovDQogICAgcHJldmlld0Zvcm0oKSB7DQogICAgICB0aGlzLmluaXRQcmV2aWV3RGlhbG9nRGF0YSgpOw0KICAgICAgdGhpcy5wcmV2aWV3T3BlbiA9IHRydWU7DQogICAgfSwNCg0KICAgIC8qKiDliJ3lp4vljJbpooTop4jlr7nor53moYbmlbDmja4gKi8NCiAgICBpbml0UHJldmlld0RpYWxvZ0RhdGEoKSB7DQogICAgICB0aGlzLnByZXZpZXdEaWFsb2dEYXRhID0ge307DQogICAgICB0aGlzLmZvcm1Nb2R1bGVzTGlzdC5mb3JFYWNoKG1vZHVsZSA9PiB7DQogICAgICAgIGlmIChtb2R1bGUuZmllbGRzKSB7DQogICAgICAgICAgbW9kdWxlLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgIGlmIChmaWVsZC5uYW1lKSB7DQogICAgICAgICAgICAgIGlmIChmaWVsZC50eXBlID09PSAnY2hlY2tib3gnKSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGlhbG9nRGF0YVtmaWVsZC5uYW1lXSA9IFtdOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGZpZWxkLnR5cGUgPT09ICdudW1iZXInKSB7DQogICAgICAgICAgICAgICAgdGhpcy5wcmV2aWV3RGlhbG9nRGF0YVtmaWVsZC5uYW1lXSA9IG51bGw7DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoZmllbGQudHlwZSA9PT0gJ2RhdGUnIHx8IGZpZWxkLnR5cGUgPT09ICd0aW1lJykgew0KICAgICAgICAgICAgICAgIHRoaXMucHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0gPSBudWxsOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMucHJldmlld0RpYWxvZ0RhdGFbZmllbGQubmFtZV0gPSAnJzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLyoqIOS/neWtmOihqOWNlemFjee9riAqLw0KICAgIHNhdmVGb3JtQ29uZmlnKCkgew0KICAgICAgaWYgKCF0aGlzLmN1cnJlbnRDYXRlZ29yeUlkKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLmnKrpgInmi6npnIDmsYLnsbvlnosiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDpqozor4HmqKHlnZfphY3nva4NCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5mb3JtTW9kdWxlc0xpc3QubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgbW9kdWxlID0gdGhpcy5mb3JtTW9kdWxlc0xpc3RbaV07DQogICAgICAgIGlmICghbW9kdWxlLm5hbWUgfHwgbW9kdWxlLm5hbWUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDnrKwke2kgKyAxfeS4quaooeWdl+WQjeensOS4jeiDveS4uuepumApOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KDQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgbW9kdWxlLmZpZWxkcy5sZW5ndGg7IGorKykgew0KICAgICAgICAgIGNvbnN0IGZpZWxkID0gbW9kdWxlLmZpZWxkc1tqXTsNCiAgICAgICAgICBpZiAoIWZpZWxkLmxhYmVsIHx8IGZpZWxkLmxhYmVsLnRyaW0oKSA9PT0gJycpIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDmqKHlnZciJHttb2R1bGUubmFtZX0i5Lit56ysJHtqICsgMX3kuKrlrZfmrrXmoIfnrb7kuI3og73kuLrnqbpgKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZm9ybURhdGEgPSB7DQogICAgICAgIGNhdGVnb3J5SWQ6IHRoaXMuY3VycmVudENhdGVnb3J5SWQsDQogICAgICAgIGZvcm1GaWVsZHM6IEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybU1vZHVsZXNMaXN0KQ0KICAgICAgfTsNCg0KICAgICAgdXBkYXRlRGVtYW5kQ2F0ZWdvcnkoZm9ybURhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLooajljZXphY3nva7kv53lrZjmiJDlip8iKTsNCiAgICAgICAgdGhpcy5mb3JtQ29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y6KGo5Y2V6YWN572u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuS/neWtmOihqOWNlemFjee9ruWksei0pSIpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlj5bmtojooajljZXphY3nva4gKi8NCiAgICBjYW5jZWxGb3JtQ29uZmlnKCkgew0KICAgICAgdGhpcy5mb3JtQ29uZmlnT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5mb3JtRmllbGRzTGlzdCA9IFtdOw0KICAgICAgdGhpcy5mb3JtTW9kdWxlc0xpc3QgPSBbXTsNCiAgICAgIHRoaXMuY3VycmVudENhdGVnb3J5SWQgPSBudWxsOw0KICAgICAgdGhpcy5zZWxlY3RlZFRlbXBsYXRlID0gJyc7DQogICAgICB0aGlzLnByZXZpZXdEYXRhID0ge307DQogICAgICB0aGlzLnByZXZpZXdEaWFsb2dEYXRhID0ge307DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6i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file": "index.vue", "sourceRoot": "src/views/miniapp/business/demandcategory", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"类型名称\" prop=\"categoryName\">\r\n        <el-input\r\n          v-model=\"queryParams.categoryName\"\r\n          placeholder=\"请输入类型名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- 隐藏新增按钮 -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:demandcategory:add']\"\r\n        >新增</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:demandcategory:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <!-- 隐藏删除按钮 -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:demandcategory:remove']\"\r\n        >删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:demandcategory:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"categoryList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      row-key=\"categoryId\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"类型ID\" align=\"center\" prop=\"categoryId\" width=\"100\" />\r\n      <el-table-column label=\"类型名称\" align=\"center\" prop=\"categoryName\" />\r\n      <el-table-column label=\"类型标识\" align=\"center\" prop=\"categoryCode\" width=\"120\" />\r\n      <el-table-column label=\"简称\" align=\"center\" prop=\"categoryShortName\" width=\"120\" />\r\n      <el-table-column label=\"图标\" align=\"center\" prop=\"categoryIcon\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview v-if=\"scope.row.categoryIcon\" :src=\"scope.row.categoryIcon\" :width=\"32\" :height=\"32\" />\r\n          <span v-else class=\"no-icon\">无图标</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"类型描述\" align=\"center\" prop=\"categoryDesc\" show-overflow-tooltip />\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input-number\r\n            v-model=\"scope.row.sortOrder\"\r\n            :min=\"0\"\r\n            size=\"mini\"\r\n            :controls=\"false\"\r\n            @change=\"handleSortChange(scope.row)\"\r\n            style=\"width: 80px;\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:demandcategory:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-setting\"\r\n            @click=\"handleFormConfig(scope.row)\"\r\n            v-hasPermi=\"['miniapp:demandcategory:edit']\"\r\n          >表单配置</el-button>\r\n          <!-- 隐藏删除按钮 -->\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:demandcategory:remove']\"\r\n          >删除</el-button> -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改需求类型对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"类型名称\" prop=\"categoryName\">\r\n              <el-input v-model=\"form.categoryName\" placeholder=\"请输入类型名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"类型标识\" prop=\"categoryCode\">\r\n              <el-input\r\n                v-model=\"form.categoryCode\"\r\n                placeholder=\"请输入类型标识代码，如：tech、business\"\r\n                :disabled=\"form.categoryId != null\"\r\n              />\r\n              <div class=\"form-tip\">\r\n                <p>• 用于标识需求类型的唯一代码</p>\r\n                <p>• 建议格式：tech、business、service等</p>\r\n                <p v-if=\"form.categoryId == null\">• 一旦设置后不可修改</p>\r\n                <p v-else style=\"color: #f56c6c;\">• 类型标识不可修改</p>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"类型简称\" prop=\"categoryShortName\">\r\n              <el-input v-model=\"form.categoryShortName\" placeholder=\"请输入类型名称简称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n              <el-input-number v-model=\"form.sortOrder\" :min=\"0\" placeholder=\"数字越小越靠前\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"类型图标\" prop=\"categoryIcon\">\r\n              <ImageUpload\r\n                v-model=\"form.categoryIcon\"\r\n                :limit=\"1\"\r\n                :fileSize=\"2\"\r\n                :isShowTip=\"true\"\r\n              />\r\n              <div class=\"form-tip\">\r\n                <p>• 支持 jpg、png、gif 格式</p>\r\n                <p>• 文件大小不超过 2MB</p>\r\n                <p>• 建议尺寸 32x32 像素，保证清晰度</p>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"类型描述\" prop=\"categoryDesc\">\r\n              <Editor\r\n                v-model=\"form.categoryDesc\"\r\n                :min-height=\"200\"\r\n                :height=\"300\"\r\n                placeholder=\"请输入类型描述...\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单字段配置对话框 -->\r\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1400px\" append-to-body>\r\n      <div class=\"form-config-container\">\r\n        <!-- 左侧配置区域 -->\r\n        <div class=\"config-area\">\r\n          <div class=\"config-header\">\r\n            <h4>字段配置</h4>\r\n            <div class=\"header-actions\">\r\n              <el-button type=\"success\" size=\"small\" @click=\"addFormModule\" icon=\"el-icon-folder-add\">添加模块</el-button>\r\n              <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">添加字段</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 预设模板 -->\r\n          <div class=\"template-section\">\r\n            <el-select v-model=\"selectedTemplate\" placeholder=\"选择预设模板\" size=\"small\" @change=\"applyTemplate\">\r\n              <el-option label=\"基础需求模板\" value=\"basic\" />\r\n              <el-option label=\"技术需求模板\" value=\"tech\" />\r\n              <el-option label=\"商务合作模板\" value=\"business\" />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 模块和字段列表 -->\r\n          <div class=\"modules-list\">\r\n            <div v-for=\"(module, moduleIndex) in formModulesList\" :key=\"moduleIndex\" class=\"module-item\">\r\n              <el-card shadow=\"hover\" class=\"module-card\">\r\n                <!-- 模块头部 -->\r\n                <div class=\"module-header\">\r\n                  <div class=\"module-title-section\">\r\n                    <!-- 模块图标 -->\r\n                    <div class=\"module-icon-section\">\r\n                      <div class=\"module-icon-display\" @click=\"openIconUploader(moduleIndex)\">\r\n                        <img v-if=\"module.icon\" :src=\"module.icon\" class=\"custom-icon\" alt=\"模块图标\" />\r\n                        <div v-else class=\"default-icon-placeholder\">\r\n                          <i class=\"el-icon-plus\"></i>\r\n                          <span>上传图标</span>\r\n                        </div>\r\n                      </div>\r\n                      <el-dropdown trigger=\"click\" @command=\"handleIconCommand($event, moduleIndex)\" v-if=\"module.icon\">\r\n                        <el-button type=\"text\" size=\"mini\" class=\"icon-edit-btn\">\r\n                          <i class=\"el-icon-edit\"></i>\r\n                        </el-button>\r\n                        <el-dropdown-menu slot=\"dropdown\">\r\n                          <el-dropdown-item command=\"upload\">重新上传</el-dropdown-item>\r\n                          <el-dropdown-item command=\"remove\">移除图标</el-dropdown-item>\r\n                        </el-dropdown-menu>\r\n                      </el-dropdown>\r\n                    </div>\r\n\r\n                    <el-input\r\n                      v-model=\"module.name\"\r\n                      placeholder=\"请输入模块名称\"\r\n                      size=\"small\"\r\n                      class=\"module-name-input\"\r\n                      @blur=\"validateModuleName(module)\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"module-actions\">\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"moveModuleUp(moduleIndex)\" :disabled=\"moduleIndex === 0\">上移</el-button>\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"moveModuleDown(moduleIndex)\" :disabled=\"moduleIndex === formModulesList.length - 1\">下移</el-button>\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"addFieldToModule(moduleIndex)\" icon=\"el-icon-plus\">添加字段</el-button>\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"removeModule(moduleIndex)\" style=\"color: #f56c6c;\">删除模块</el-button>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n\r\n                <!-- 字段列表 -->\r\n                <div class=\"fields-list\" v-if=\"module.fields && module.fields.length > 0\">\r\n                  <div v-for=\"(field, fieldIndex) in module.fields\" :key=\"fieldIndex\" class=\"field-item\">\r\n                    <el-card shadow=\"hover\" class=\"field-card\">\r\n                      <div class=\"field-header\">\r\n                        <span class=\"field-title\">\r\n                          {{ field.label || '未命名字段' }}\r\n                          <el-tag v-if=\"field.hidden\" size=\"mini\" type=\"info\" style=\"margin-left: 8px;\">隐藏</el-tag>\r\n                        </span>\r\n                        <div class=\"field-actions\">\r\n                          <el-button type=\"text\" size=\"mini\" @click=\"moveFieldUpInModule(moduleIndex, fieldIndex)\" :disabled=\"fieldIndex === 0\">上移</el-button>\r\n                          <el-button type=\"text\" size=\"mini\" @click=\"moveFieldDownInModule(moduleIndex, fieldIndex)\" :disabled=\"fieldIndex === module.fields.length - 1\">下移</el-button>\r\n                          <el-button type=\"text\" size=\"mini\" @click=\"removeFieldFromModule(moduleIndex, fieldIndex)\" style=\"color: #f56c6c;\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <el-form :model=\"field\" label-width=\"80px\" size=\"small\">\r\n                        <el-form-item label=\"字段标签\">\r\n                          <el-input v-model=\"field.label\" placeholder=\"请输入字段标签\" @input=\"generateFieldName(field)\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"字段类型\">\r\n                          <el-select v-model=\"field.type\" placeholder=\"请选择字段类型\" @change=\"onFieldTypeChange(field)\">\r\n                            <el-option label=\"文本输入\" value=\"input\" />\r\n                            <el-option label=\"多行文本\" value=\"textarea\" />\r\n                            <el-option label=\"数字输入\" value=\"number\" />\r\n                            <el-option label=\"电话号码\" value=\"tel\" />\r\n                            <el-option label=\"邮箱地址\" value=\"email\" />\r\n                            <el-option label=\"单选框\" value=\"radio\" />\r\n                            <el-option label=\"多选框\" value=\"checkbox\" />\r\n                            <el-option label=\"下拉选择\" value=\"select\" />\r\n                            <el-option label=\"日期选择\" value=\"date\" />\r\n                            <el-option label=\"时间选择\" value=\"time\" />\r\n                            <el-option label=\"文件上传\" value=\"file\" />\r\n                            <el-option label=\"静态展示\" value=\"static\" />\r\n                          </el-select>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"提示文字\" v-if=\"field.type !== 'static'\">\r\n                          <el-input v-model=\"field.placeholder\" placeholder=\"请输入提示文字，如：请输入姓名\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"显示内容\" v-if=\"field.type === 'static'\">\r\n                          <el-input v-model=\"field.staticContent\" type=\"textarea\" placeholder=\"请输入要显示的静态内容\" :rows=\"3\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"是否必填\" v-if=\"field.type !== 'static'\">\r\n                          <el-switch v-model=\"field.required\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"是否隐藏\">\r\n                          <el-switch v-model=\"field.hidden\" @change=\"onFieldHiddenChange(field)\" />\r\n                          <div class=\"field-tip\" v-if=\"field.hidden\">\r\n                            <i class=\"el-icon-info\"></i>\r\n                            <span>隐藏的字段在小程序端不会显示给用户</span>\r\n                          </div>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"选项配置\" v-if=\"['radio', 'checkbox', 'select'].includes(field.type)\">\r\n                          <el-input v-model=\"field.options\" type=\"textarea\" placeholder=\"请输入选项，用逗号分隔\" :rows=\"2\" />\r\n                        </el-form-item>\r\n                      </el-form>\r\n                    </el-card>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 空模块提示 -->\r\n                <div v-else class=\"empty-module\">\r\n                  <el-empty description=\"暂无字段\" :image-size=\"60\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"addFieldToModule(moduleIndex)\">添加字段</el-button>\r\n                  </el-empty>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"formModulesList.length === 0\" class=\"empty-modules\">\r\n              <el-empty description=\"暂无模块\" :image-size=\"80\">\r\n                <el-button type=\"primary\" @click=\"addFormModule\">创建第一个模块</el-button>\r\n              </el-empty>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧预览区域 -->\r\n        <div class=\"preview-area\">\r\n          <div class=\"preview-header\">\r\n            <h4>表单预览</h4>\r\n            <el-button type=\"success\" size=\"small\" @click=\"previewForm\">预览表单</el-button>\r\n          </div>\r\n          <div class=\"preview-content\">\r\n            <div v-for=\"(module, moduleIndex) in formModulesList\" :key=\"moduleIndex\" class=\"preview-module\">\r\n              <div class=\"preview-module-header\">\r\n                <div class=\"preview-module-title\">\r\n                  <img v-if=\"module.icon\" :src=\"module.icon\" class=\"preview-module-icon-img\" alt=\"模块图标\" />\r\n                  <i v-else class=\"el-icon-folder-opened preview-module-icon\"></i>\r\n                  <h5>{{ module.name || '未命名模块' }}</h5>\r\n                </div>\r\n              </div>\r\n              <el-form label-width=\"100px\" size=\"small\" v-if=\"module.fields && module.fields.length > 0\">\r\n                <el-form-item v-for=\"field in module.fields\" :key=\"field.name\" :label=\"field.label\" :required=\"field.required && field.type !== 'static'\" :class=\"{ 'hidden-field': field.hidden }\">\r\n                  <div v-if=\"field.hidden\" class=\"hidden-field-indicator\">\r\n                    <el-tag size=\"mini\" type=\"info\">隐藏字段</el-tag>\r\n                  </div>\r\n                  <!-- 静态展示 -->\r\n                  <div v-if=\"field.type === 'static'\" class=\"static-content\">\r\n                    {{ field.staticContent || '暂无内容' }}\r\n                  </div>\r\n                  <!-- 文本输入 -->\r\n                  <el-input v-else-if=\"field.type === 'input'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入'\" disabled />\r\n                  <!-- 多行文本 -->\r\n                  <el-input v-else-if=\"field.type === 'textarea'\" v-model=\"previewData[field.name]\" type=\"textarea\" :placeholder=\"field.placeholder || '请输入'\" disabled />\r\n                  <!-- 数字输入 -->\r\n                  <el-input-number v-else-if=\"field.type === 'number'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入数字'\" disabled />\r\n                  <!-- 电话号码 -->\r\n                  <el-input v-else-if=\"field.type === 'tel'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入电话号码'\" disabled />\r\n                  <!-- 邮箱地址 -->\r\n                  <el-input v-else-if=\"field.type === 'email'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入邮箱地址'\" disabled />\r\n                  <!-- 单选框 -->\r\n                  <el-radio-group v-else-if=\"field.type === 'radio'\" v-model=\"previewData[field.name]\" disabled>\r\n                    <el-radio v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-radio>\r\n                  </el-radio-group>\r\n                  <!-- 多选框 -->\r\n                  <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" v-model=\"previewData[field.name]\" disabled>\r\n                    <el-checkbox v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-checkbox>\r\n                  </el-checkbox-group>\r\n                  <!-- 下拉选择 -->\r\n                  <el-select v-else-if=\"field.type === 'select'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请选择'\" disabled>\r\n                    <el-option v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\" :value=\"option\" />\r\n                  </el-select>\r\n                  <!-- 日期选择 -->\r\n                  <el-date-picker v-else-if=\"field.type === 'date'\" v-model=\"previewData[field.name]\" type=\"date\" :placeholder=\"field.placeholder || '请选择日期'\" disabled />\r\n                  <!-- 时间选择 -->\r\n                  <el-time-picker v-else-if=\"field.type === 'time'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请选择时间'\" disabled />\r\n                  <!-- 文件上传 -->\r\n                  <el-upload v-else-if=\"field.type === 'file'\" action=\"#\" disabled>\r\n                    <el-button size=\"small\" type=\"primary\" disabled>{{ field.placeholder || '点击上传' }}</el-button>\r\n                  </el-upload>\r\n                </el-form-item>\r\n              </el-form>\r\n              <div v-else class=\"preview-empty-module\">\r\n                <span>该模块暂无字段</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"formModulesList.length === 0\" class=\"preview-empty\">\r\n              <el-empty description=\"暂无表单配置\" :image-size=\"60\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\r\n        <el-button @click=\"cancelFormConfig\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewOpen\" width=\"800px\" append-to-body>\r\n      <div class=\"preview-dialog-content\">\r\n        <div v-for=\"(module, moduleIndex) in formModulesList\" :key=\"moduleIndex\" class=\"preview-dialog-module\">\r\n          <div class=\"preview-dialog-module-header\">\r\n            <div class=\"preview-dialog-module-title\">\r\n              <img v-if=\"module.icon\" :src=\"module.icon\" class=\"preview-dialog-module-icon-img\" alt=\"模块图标\" />\r\n              <i v-else class=\"el-icon-folder-opened preview-dialog-module-icon\"></i>\r\n              <h4>{{ module.name || '未命名模块' }}</h4>\r\n            </div>\r\n          </div>\r\n          <el-form label-width=\"100px\" v-if=\"module.fields && module.fields.length > 0\">\r\n            <el-form-item v-for=\"field in module.fields\" :key=\"field.name\" :label=\"field.label\" :required=\"field.required && field.type !== 'static'\" :class=\"{ 'hidden-field': field.hidden }\">\r\n              <div v-if=\"field.hidden\" class=\"hidden-field-indicator\">\r\n                <el-tag size=\"mini\" type=\"info\">隐藏字段</el-tag>\r\n              </div>\r\n              <!-- 静态展示 -->\r\n              <div v-if=\"field.type === 'static'\" class=\"static-content\">\r\n                {{ field.staticContent || '暂无内容' }}\r\n              </div>\r\n              <!-- 文本输入 -->\r\n              <el-input v-else-if=\"field.type === 'input'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入'\" />\r\n              <!-- 多行文本 -->\r\n              <el-input v-else-if=\"field.type === 'textarea'\" v-model=\"previewDialogData[field.name]\" type=\"textarea\" :placeholder=\"field.placeholder || '请输入'\" />\r\n              <!-- 数字输入 -->\r\n              <el-input-number v-else-if=\"field.type === 'number'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入数字'\" />\r\n              <!-- 电话号码 -->\r\n              <el-input v-else-if=\"field.type === 'tel'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入电话号码'\" />\r\n              <!-- 邮箱地址 -->\r\n              <el-input v-else-if=\"field.type === 'email'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入邮箱地址'\" />\r\n              <!-- 单选框 -->\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" v-model=\"previewDialogData[field.name]\">\r\n                <el-radio v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-radio>\r\n              </el-radio-group>\r\n              <!-- 多选框 -->\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" v-model=\"previewDialogData[field.name]\">\r\n                <el-checkbox v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-checkbox>\r\n              </el-checkbox-group>\r\n              <!-- 下拉选择 -->\r\n              <el-select v-else-if=\"field.type === 'select'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请选择'\">\r\n                <el-option v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\" :value=\"option\" />\r\n              </el-select>\r\n              <!-- 日期选择 -->\r\n              <el-date-picker v-else-if=\"field.type === 'date'\" v-model=\"previewDialogData[field.name]\" type=\"date\" :placeholder=\"field.placeholder || '请选择日期'\" />\r\n              <!-- 时间选择 -->\r\n              <el-time-picker v-else-if=\"field.type === 'time'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请选择时间'\" />\r\n              <!-- 文件上传 -->\r\n              <el-upload v-else-if=\"field.type === 'file'\" action=\"#\">\r\n                <el-button size=\"small\" type=\"primary\">{{ field.placeholder || '点击上传' }}</el-button>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-form>\r\n          <div v-else class=\"preview-dialog-empty-module\">\r\n            <span>该模块暂无字段</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"formModulesList.length === 0\" class=\"preview-dialog-empty\">\r\n          <el-empty description=\"暂无表单配置\" :image-size=\"60\" />\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图标上传对话框 -->\r\n    <el-dialog title=\"上传模块图标\" :visible.sync=\"iconUploaderOpen\" width=\"500px\" append-to-body>\r\n      <div class=\"icon-uploader-content\">\r\n        <div class=\"upload-section\">\r\n          <h4>上传自定义图标</h4>\r\n          <ImageUpload\r\n            v-model=\"uploadedIcon\"\r\n            :limit=\"1\"\r\n            :fileSize=\"2\"\r\n            :isShowTip=\"true\"\r\n          />\r\n          <div class=\"upload-tips\">\r\n            <p>• 支持 jpg、png、gif 格式</p>\r\n            <p>• 文件大小不超过 2MB</p>\r\n            <p>• 建议尺寸 32x32 像素，保证清晰度</p>\r\n            <p>• 建议使用透明背景的PNG格式</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelIconUpload\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmIconUpload\" :disabled=\"!uploadedIcon\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDemandCategory, getDemandCategory, delDemandCategory, addDemandCategory, updateDemandCategory } from \"@/api/miniapp/demandcategory\";\r\n\r\nexport default {\r\n  name: \"MiniDemandCategory\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 需求类型表格数据\r\n      categoryList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        categoryName: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        categoryName: [\r\n          { required: true, message: \"类型名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        categoryCode: [\r\n          { required: true, message: \"类型标识不能为空\", trigger: \"blur\" },\r\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: \"类型标识必须以字母开头，只能包含字母、数字和下划线\", trigger: \"blur\" }\r\n        ],\r\n        status: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 表单配置相关\r\n      formConfigOpen: false,\r\n      previewOpen: false,\r\n      currentCategoryId: null,\r\n      formFieldsList: [], // 保留兼容性\r\n      formModulesList: [], // 新的模块化结构\r\n      selectedTemplate: '',\r\n      previewData: {}, // 预览区域的表单数据\r\n      previewDialogData: {}, // 预览对话框的表单数据\r\n      // 图标相关\r\n      iconUploaderOpen: false,\r\n      currentModuleIndex: -1,\r\n      uploadedIcon: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n\r\n    /** 查询需求类型列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDemandCategory(this.queryParams).then(response => {\r\n        this.categoryList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        categoryId: null,\r\n        categoryName: null,\r\n        categoryCode: null,\r\n        categoryShortName: null,\r\n        categoryIcon: null,\r\n        categoryDesc: null,\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.categoryId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加需求类型\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const categoryId = row.categoryId || this.ids;\r\n      getDemandCategory(categoryId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改需求类型\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.categoryId != null) {\r\n            updateDemandCategory(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDemandCategory(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const categoryIds = row.categoryId || this.ids;\r\n      this.$modal.confirm('是否确认删除需求类型编号为\"' + categoryIds + '\"的数据项？').then(function() {\r\n        return delDemandCategory(categoryIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/demandcategory/export', {\r\n        ...this.queryParams\r\n      }, `需求类型数据_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 排序修改 */\r\n    handleSortChange(row) {\r\n      updateDemandCategory(row).then(response => {\r\n        this.$modal.msgSuccess(\"排序修改成功\");\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig(row) {\r\n      this.currentCategoryId = row.categoryId;\r\n      this.formFieldsList = [];\r\n      this.formModulesList = [];\r\n\r\n      // 如果已有配置，解析JSON\r\n      if (row.formFields) {\r\n        try {\r\n          const parsedData = JSON.parse(row.formFields);\r\n\r\n          // 检查是否为新的模块化结构\r\n          if (Array.isArray(parsedData) && parsedData.length > 0 && parsedData[0].hasOwnProperty('name')) {\r\n            // 新的模块化结构\r\n            this.formModulesList = parsedData;\r\n            // 确保所有字段都有 hidden 属性\r\n            this.formModulesList.forEach(module => {\r\n              if (module.fields) {\r\n                module.fields.forEach(field => {\r\n                  if (field.hidden === undefined) {\r\n                    this.$set(field, 'hidden', false);\r\n                  }\r\n                });\r\n              }\r\n            });\r\n          } else if (Array.isArray(parsedData)) {\r\n            // 旧的字段列表结构，转换为模块化结构\r\n            this.formFieldsList = parsedData;\r\n            if (parsedData.length > 0) {\r\n              // 为旧字段添加 hidden 属性\r\n              parsedData.forEach(field => {\r\n                if (field.hidden === undefined) {\r\n                  this.$set(field, 'hidden', false);\r\n                }\r\n              });\r\n              this.formModulesList = [{\r\n                name: '基础信息',\r\n                description: '',\r\n                fields: parsedData\r\n              }];\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单配置失败:', e);\r\n          this.formFieldsList = [];\r\n          this.formModulesList = [];\r\n        }\r\n      }\r\n\r\n      this.formConfigOpen = true;\r\n      this.initPreviewData();\r\n    },\r\n\r\n    /** 初始化预览数据 */\r\n    initPreviewData() {\r\n      this.previewData = {};\r\n      this.formModulesList.forEach(module => {\r\n        if (module.fields) {\r\n          module.fields.forEach(field => {\r\n            if (field.name) {\r\n              if (field.type === 'checkbox') {\r\n                this.previewData[field.name] = [];\r\n              } else if (field.type === 'number') {\r\n                this.previewData[field.name] = null;\r\n              } else if (field.type === 'date' || field.type === 'time') {\r\n                this.previewData[field.name] = null;\r\n              } else {\r\n                this.previewData[field.name] = '';\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 添加表单模块 */\r\n    addFormModule() {\r\n      const newModule = {\r\n        name: '新模块',\r\n        description: '',\r\n        fields: [],\r\n        icon: '' // 默认无图标，需要用户上传\r\n      };\r\n      this.formModulesList.push(newModule);\r\n    },\r\n\r\n    /** 删除模块 */\r\n    removeModule(moduleIndex) {\r\n      this.$confirm('确认删除该模块及其所有字段吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.formModulesList.splice(moduleIndex, 1);\r\n        this.$message.success('删除成功');\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 模块上移 */\r\n    moveModuleUp(moduleIndex) {\r\n      if (moduleIndex > 0) {\r\n        const temp = this.formModulesList[moduleIndex];\r\n        this.$set(this.formModulesList, moduleIndex, this.formModulesList[moduleIndex - 1]);\r\n        this.$set(this.formModulesList, moduleIndex - 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 模块下移 */\r\n    moveModuleDown(moduleIndex) {\r\n      if (moduleIndex < this.formModulesList.length - 1) {\r\n        const temp = this.formModulesList[moduleIndex];\r\n        this.$set(this.formModulesList, moduleIndex, this.formModulesList[moduleIndex + 1]);\r\n        this.$set(this.formModulesList, moduleIndex + 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 验证模块名称 */\r\n    validateModuleName(module) {\r\n      if (!module.name || module.name.trim() === '') {\r\n        module.name = '未命名模块';\r\n      }\r\n    },\r\n\r\n    /** 向模块添加字段 */\r\n    addFieldToModule(moduleIndex) {\r\n      const newField = {\r\n        label: '',\r\n        name: '',\r\n        type: 'input',\r\n        required: false,\r\n        hidden: false, // 默认不隐藏\r\n        options: '',\r\n        placeholder: '请输入',\r\n        staticContent: ''\r\n      };\r\n      if (!this.formModulesList[moduleIndex].fields) {\r\n        this.$set(this.formModulesList[moduleIndex], 'fields', []);\r\n      }\r\n      this.formModulesList[moduleIndex].fields.push(newField);\r\n      // 更新预览数据\r\n      this.$nextTick(() => {\r\n        this.initPreviewData();\r\n      });\r\n    },\r\n\r\n    /** 处理图标命令 */\r\n    handleIconCommand(command, moduleIndex) {\r\n      this.currentModuleIndex = moduleIndex;\r\n\r\n      if (command === 'upload') {\r\n        this.uploadedIcon = '';\r\n        this.iconUploaderOpen = true;\r\n      } else if (command === 'remove') {\r\n        this.$set(this.formModulesList[moduleIndex], 'icon', '');\r\n      }\r\n    },\r\n\r\n    /** 打开图标上传器 */\r\n    openIconUploader(moduleIndex) {\r\n      this.currentModuleIndex = moduleIndex;\r\n      this.uploadedIcon = '';\r\n      this.iconUploaderOpen = true;\r\n    },\r\n\r\n    /** 确认图标上传 */\r\n    confirmIconUpload() {\r\n      if (this.currentModuleIndex >= 0 && this.uploadedIcon) {\r\n        this.$set(this.formModulesList[this.currentModuleIndex], 'icon', this.uploadedIcon);\r\n        this.$message.success('图标上传成功');\r\n      }\r\n      this.iconUploaderOpen = false;\r\n      this.currentModuleIndex = -1;\r\n      this.uploadedIcon = '';\r\n    },\r\n\r\n    /** 取消图标上传 */\r\n    cancelIconUpload() {\r\n      this.iconUploaderOpen = false;\r\n      this.currentModuleIndex = -1;\r\n      this.uploadedIcon = '';\r\n    },\r\n\r\n    /** 字段类型变化时的处理 */\r\n    onFieldTypeChange(field) {\r\n      // 根据字段类型设置默认的placeholder\r\n      const placeholderMap = {\r\n        'input': '请输入',\r\n        'textarea': '请输入',\r\n        'number': '请输入数字',\r\n        'tel': '请输入电话号码',\r\n        'email': '请输入邮箱地址',\r\n        'radio': '',\r\n        'checkbox': '',\r\n        'select': '请选择',\r\n        'date': '请选择日期',\r\n        'time': '请选择时间',\r\n        'file': '点击上传',\r\n        'static': ''\r\n      };\r\n\r\n      if (!field.placeholder || field.placeholder === '') {\r\n        field.placeholder = placeholderMap[field.type] || '请输入';\r\n      }\r\n\r\n      // 如果是静态展示字段，设置默认内容和清除必填状态\r\n      if (field.type === 'static') {\r\n        field.required = false;\r\n        if (!field.staticContent) {\r\n          field.staticContent = '这里是静态展示内容';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 字段隐藏状态变化时的处理 */\r\n    onFieldHiddenChange() {\r\n      // 触发视图更新，无需额外处理\r\n    },\r\n\r\n    /** 从模块删除字段 */\r\n    removeFieldFromModule(moduleIndex, fieldIndex) {\r\n      this.formModulesList[moduleIndex].fields.splice(fieldIndex, 1);\r\n    },\r\n\r\n    /** 模块内字段上移 */\r\n    moveFieldUpInModule(moduleIndex, fieldIndex) {\r\n      const fields = this.formModulesList[moduleIndex].fields;\r\n      if (fieldIndex > 0) {\r\n        const temp = fields[fieldIndex];\r\n        this.$set(fields, fieldIndex, fields[fieldIndex - 1]);\r\n        this.$set(fields, fieldIndex - 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 模块内字段下移 */\r\n    moveFieldDownInModule(moduleIndex, fieldIndex) {\r\n      const fields = this.formModulesList[moduleIndex].fields;\r\n      if (fieldIndex < fields.length - 1) {\r\n        const temp = fields[fieldIndex];\r\n        this.$set(fields, fieldIndex, fields[fieldIndex + 1]);\r\n        this.$set(fields, fieldIndex + 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 添加表单字段（兼容旧方法） */\r\n    addFormField() {\r\n      // 如果没有模块，先创建一个默认模块\r\n      if (this.formModulesList.length === 0) {\r\n        this.addFormModule();\r\n        this.formModulesList[0].name = '基础信息';\r\n      }\r\n\r\n      // 添加到第一个模块\r\n      this.addFieldToModule(0);\r\n    },\r\n\r\n\r\n\r\n    /** 生成字段名称 */\r\n    generateFieldName(field) {\r\n      if (field.label) {\r\n        // 扩展的中文转英文映射\r\n        const nameMap = {\r\n          // 基础信息\r\n          '姓名': 'name',\r\n          '联系人': 'contact_name',\r\n          '联系电话': 'phone',\r\n          '手机号': 'phone',\r\n          '电话': 'phone',\r\n          '邮箱': 'email',\r\n          '邮箱地址': 'email',\r\n          '公司': 'company',\r\n          '公司名称': 'company_name',\r\n          '职位': 'position',\r\n          '部门': 'department',\r\n          '地址': 'address',\r\n          '详细地址': 'detailed_address',\r\n\r\n          // 技术相关\r\n          '技术方向': 'tech_direction',\r\n          '技术栈': 'tech_stack',\r\n          '开发语言': 'programming_language',\r\n          '项目周期': 'project_duration',\r\n          '预算范围': 'budget_range',\r\n          '项目详细需求': 'detailed_requirements',\r\n          '技术要求': 'tech_requirements',\r\n\r\n          // 市场推广相关\r\n          '推广类型': 'promotion_type',\r\n          '目标客户群体': 'target_audience',\r\n          '推广渠道': 'promotion_channels',\r\n          '推广预算': 'promotion_budget',\r\n          '推广时间': 'promotion_duration',\r\n          '推广目标': 'promotion_goals',\r\n\r\n          // 招聘相关\r\n          '招聘职位': 'job_position',\r\n          '工作经验': 'work_experience',\r\n          '学历要求': 'education_requirement',\r\n          '薪资范围': 'salary_range',\r\n          '工作地点': 'work_location',\r\n          '职位描述': 'job_description',\r\n\r\n          // 投资相关\r\n          '投资类型': 'investment_type',\r\n          '投资金额': 'investment_amount',\r\n          '投资阶段': 'investment_stage',\r\n          '行业领域': 'industry_field',\r\n          '项目介绍': 'project_introduction',\r\n\r\n          // 采购相关\r\n          '产品名称': 'product_name',\r\n          '采购数量': 'purchase_quantity',\r\n          '质量要求': 'quality_requirements',\r\n          '交付时间': 'delivery_time',\r\n          '采购预算': 'purchase_budget',\r\n\r\n          // 通用字段\r\n          '需求描述': 'description',\r\n          '详细说明': 'detailed_description',\r\n          '备注': 'remark',\r\n          '说明': 'note',\r\n          '标题': 'title',\r\n          '内容': 'content',\r\n          '时间': 'time',\r\n          '日期': 'date',\r\n          '文件': 'file',\r\n          '图片': 'image',\r\n          '附件': 'attachment'\r\n        };\r\n\r\n        // 如果有直接映射，使用映射值\r\n        if (nameMap[field.label]) {\r\n          field.name = nameMap[field.label];\r\n        } else {\r\n          // 否则进行智能转换\r\n          let name = field.label\r\n            .replace(/[\\s\\-\\/\\\\]/g, '_') // 替换空格、横线、斜线为下划线\r\n            .replace(/[^\\w\\u4e00-\\u9fa5]/g, '') // 移除特殊字符，保留中英文和数字\r\n            .toLowerCase();\r\n\r\n          // 如果包含中文，尝试转换为拼音或英文\r\n          if (/[\\u4e00-\\u9fa5]/.test(name)) {\r\n            // 简单的中文关键词替换\r\n            name = name\r\n              .replace(/类型/g, 'type')\r\n              .replace(/名称/g, 'name')\r\n              .replace(/时间/g, 'time')\r\n              .replace(/日期/g, 'date')\r\n              .replace(/地址/g, 'address')\r\n              .replace(/电话/g, 'phone')\r\n              .replace(/邮箱/g, 'email')\r\n              .replace(/公司/g, 'company')\r\n              .replace(/描述/g, 'description')\r\n              .replace(/说明/g, 'note')\r\n              .replace(/备注/g, 'remark')\r\n              .replace(/要求/g, 'requirement')\r\n              .replace(/范围/g, 'range')\r\n              .replace(/预算/g, 'budget')\r\n              .replace(/数量/g, 'quantity')\r\n              .replace(/价格/g, 'price')\r\n              .replace(/费用/g, 'cost');\r\n\r\n            // 如果还有中文，使用拼音首字母或保持原样\r\n            if (/[\\u4e00-\\u9fa5]/.test(name)) {\r\n              name = 'field_' + Date.now().toString().slice(-6); // 使用时间戳后6位作为唯一标识\r\n            }\r\n          }\r\n\r\n          field.name = name;\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 应用预设模板 */\r\n    applyTemplate() {\r\n      if (this.selectedTemplate === 'basic') {\r\n        this.applyBasicTemplate();\r\n      } else if (this.selectedTemplate === 'tech') {\r\n        this.applyTechTemplate();\r\n      } else if (this.selectedTemplate === 'business') {\r\n        this.applyBusinessTemplate();\r\n      }\r\n      // 应用模板后初始化预览数据\r\n      this.$nextTick(() => {\r\n        this.initPreviewData();\r\n      });\r\n    },\r\n\r\n    /** 应用基础需求模板 */\r\n    applyBasicTemplate() {\r\n      this.formModulesList = [\r\n        {\r\n          name: '填写说明',\r\n          description: '请仔细阅读以下说明后填写表单',\r\n          fields: [\r\n            {\r\n              label: '温馨提示',\r\n              name: 'tips',\r\n              type: 'static',\r\n              required: false,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '',\r\n              staticContent: '请如实填写以下信息，我们将在24小时内与您取得联系。带*号的为必填项。'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '基础信息',\r\n          description: '请填写需求的基本信息',\r\n          fields: [\r\n            {\r\n              label: '需求标题',\r\n              name: 'title',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入需求标题',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '需求描述',\r\n              name: 'description',\r\n              type: 'textarea',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请详细描述您的需求',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '联系方式',\r\n          description: '请填写您的联系方式，以便我们与您取得联系',\r\n          fields: [\r\n            {\r\n              label: '联系人',\r\n              name: 'contact_name',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入联系人姓名',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '联系电话',\r\n              name: 'phone',\r\n              type: 'tel',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入手机号码',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 确保字段名称正确生成\r\n      this.formModulesList.forEach(module => {\r\n        module.fields.forEach(field => {\r\n          this.generateFieldName(field);\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 应用技术需求模板 */\r\n    applyTechTemplate() {\r\n      this.formModulesList = [\r\n        {\r\n          name: '技术需求',\r\n          description: '请详细描述您的技术需求',\r\n          fields: [\r\n            {\r\n              label: '技术方向',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '前端开发,后端开发,移动开发,人工智能,大数据,云计算',\r\n              placeholder: '请选择技术方向',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '技术栈',\r\n              name: '',\r\n              type: 'checkbox',\r\n              required: false,\r\n              hidden: false,\r\n              options: 'Java,Python,JavaScript,React,Vue,Spring Boot,MySQL,Redis',\r\n              placeholder: '',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '项目详细需求',\r\n              name: '',\r\n              type: 'textarea',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请详细描述项目需求、功能要求、技术要求等',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '项目信息',\r\n          description: '请填写项目的基本信息',\r\n          fields: [\r\n            {\r\n              label: '项目周期',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '1周内,1-2周,2-4周,1-2个月,2-3个月,3个月以上',\r\n              placeholder: '请选择项目周期',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '预算范围',\r\n              name: '',\r\n              type: 'radio',\r\n              required: true,\r\n              hidden: false,\r\n              options: '1万以下,1-5万,5-10万,10-20万,20万以上',\r\n              placeholder: '',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '联系方式',\r\n          description: '请填写您的联系方式',\r\n          fields: [\r\n            {\r\n              label: '联系人',\r\n              name: '',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入联系人姓名',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '联系电话',\r\n              name: '',\r\n              type: 'tel',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入手机号码',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 自动生成字段名称\r\n      this.formModulesList.forEach(module => {\r\n        module.fields.forEach(field => {\r\n          this.generateFieldName(field);\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 应用商务合作模板 */\r\n    applyBusinessTemplate() {\r\n      this.formModulesList = [\r\n        {\r\n          name: '合作信息',\r\n          description: '请填写合作相关信息',\r\n          fields: [\r\n            {\r\n              label: '合作类型',\r\n              name: '',\r\n              type: 'radio',\r\n              required: true,\r\n              hidden: false,\r\n              options: '战略合作,技术合作,市场合作,投资合作',\r\n              placeholder: '',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '合作描述',\r\n              name: '',\r\n              type: 'textarea',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请详细描述合作内容、合作方式、期望达成的目标等',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '公司信息',\r\n          description: '请填写您的公司基本信息',\r\n          fields: [\r\n            {\r\n              label: '公司名称',\r\n              name: '',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入公司全称',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '公司规模',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '10人以下,10-50人,50-200人,200-500人,500人以上',\r\n              placeholder: '请选择公司规模',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '行业领域',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '互联网,金融,教育,医疗,制造业,服务业,其他',\r\n              placeholder: '请选择行业领域',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '联系方式',\r\n          description: '请填写联系方式，以便我们与您取得联系',\r\n          fields: [\r\n            {\r\n              label: '联系人',\r\n              name: '',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入联系人姓名',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '联系电话',\r\n              name: '',\r\n              type: 'tel',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入手机号码',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '邮箱地址',\r\n              name: '',\r\n              type: 'email',\r\n              required: false,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入邮箱地址（选填）',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 自动生成字段名称\r\n      this.formModulesList.forEach(module => {\r\n        module.fields.forEach(field => {\r\n          this.generateFieldName(field);\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 获取字段选项 */\r\n    getFieldOptions(field) {\r\n      if (!field.options || field.options.trim() === '') return [];\r\n      return field.options.split(',').map(option => option.trim()).filter(option => option !== '');\r\n    },\r\n\r\n    /** 预览表单 */\r\n    previewForm() {\r\n      this.initPreviewDialogData();\r\n      this.previewOpen = true;\r\n    },\r\n\r\n    /** 初始化预览对话框数据 */\r\n    initPreviewDialogData() {\r\n      this.previewDialogData = {};\r\n      this.formModulesList.forEach(module => {\r\n        if (module.fields) {\r\n          module.fields.forEach(field => {\r\n            if (field.name) {\r\n              if (field.type === 'checkbox') {\r\n                this.previewDialogData[field.name] = [];\r\n              } else if (field.type === 'number') {\r\n                this.previewDialogData[field.name] = null;\r\n              } else if (field.type === 'date' || field.type === 'time') {\r\n                this.previewDialogData[field.name] = null;\r\n              } else {\r\n                this.previewDialogData[field.name] = '';\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (!this.currentCategoryId) {\r\n        this.$modal.msgError(\"未选择需求类型\");\r\n        return;\r\n      }\r\n\r\n      // 验证模块配置\r\n      for (let i = 0; i < this.formModulesList.length; i++) {\r\n        const module = this.formModulesList[i];\r\n        if (!module.name || module.name.trim() === '') {\r\n          this.$modal.msgError(`第${i + 1}个模块名称不能为空`);\r\n          return;\r\n        }\r\n\r\n        for (let j = 0; j < module.fields.length; j++) {\r\n          const field = module.fields[j];\r\n          if (!field.label || field.label.trim() === '') {\r\n            this.$modal.msgError(`模块\"${module.name}\"中第${j + 1}个字段标签不能为空`);\r\n            return;\r\n          }\r\n        }\r\n      }\r\n\r\n      const formData = {\r\n        categoryId: this.currentCategoryId,\r\n        formFields: JSON.stringify(this.formModulesList)\r\n      };\r\n\r\n      updateDemandCategory(formData).then(() => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.getList();\r\n      }).catch(error => {\r\n        console.error('保存表单配置失败:', error);\r\n        this.$modal.msgError(\"保存表单配置失败\");\r\n      });\r\n    },\r\n\r\n    /** 取消表单配置 */\r\n    cancelFormConfig() {\r\n      this.formConfigOpen = false;\r\n      this.formFieldsList = [];\r\n      this.formModulesList = [];\r\n      this.currentCategoryId = null;\r\n      this.selectedTemplate = '';\r\n      this.previewData = {};\r\n      this.previewDialogData = {};\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  display: flex;\r\n  height: 700px;\r\n  gap: 20px;\r\n}\r\n\r\n.config-area {\r\n  flex: 1;\r\n  border-right: 1px solid #e6e6e6;\r\n  padding-right: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-area {\r\n  flex: 1;\r\n  padding-left: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.config-header, .preview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.config-header h4, .preview-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.template-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.modules-list {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.module-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.module-card {\r\n  border: 2px solid #e6e6e6;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.module-card:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.module-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.module-title-section {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.module-icon {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.module-name-input {\r\n  max-width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.module-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n\r\n\r\n.fields-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.field-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.field-card {\r\n  border: 1px solid #e6e6e6;\r\n  margin-left: 20px;\r\n}\r\n\r\n.field-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.field-title {\r\n  font-weight: bold;\r\n  color: #606266;\r\n}\r\n\r\n.field-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.empty-module {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-modules {\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n.preview-content {\r\n  background-color: #f9f9f9;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  min-height: 500px;\r\n}\r\n\r\n.preview-module {\r\n  margin-bottom: 30px;\r\n  background-color: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.preview-module-header {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409EFF;\r\n}\r\n\r\n.preview-module-header h5 {\r\n  margin: 0 0 5px 0;\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n\r\n\r\n.preview-empty-module {\r\n  text-align: center;\r\n  color: #c0c4cc;\r\n  padding: 20px;\r\n  font-style: italic;\r\n}\r\n\r\n.preview-empty {\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n/* 隐藏字段样式 */\r\n.hidden-field {\r\n  opacity: 0.6;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 8px;\r\n  margin-bottom: 8px;\r\n  border-left: 3px solid #909399;\r\n}\r\n\r\n.hidden-field-indicator {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.field-tip {\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.field-tip i {\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 表单对话框样式优化 */\r\n.el-dialog__body {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.el-form-item {\r\n  margin-bottom: 18px;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.preview-dialog-content {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-dialog-module {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  border: 1px solid #e6e6e6;\r\n  border-radius: 8px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.preview-dialog-module-header {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409EFF;\r\n}\r\n\r\n.preview-dialog-module-header h4 {\r\n  margin: 0 0 5px 0;\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n}\r\n\r\n\r\n\r\n.preview-dialog-empty-module {\r\n  text-align: center;\r\n  color: #c0c4cc;\r\n  padding: 20px;\r\n  font-style: italic;\r\n}\r\n\r\n.preview-dialog-empty {\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n.el-card {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.el-form-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 静态展示内容样式 */\r\n.static-content {\r\n  padding: 10px 15px;\r\n  background-color: #f5f7fa;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n/* 模块图标样式 */\r\n.module-icon-section {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.module-icon-display {\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  margin-right: 4px;\r\n  background-color: #fff;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.module-icon-display:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.custom-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  object-fit: cover;\r\n  border-radius: 2px;\r\n}\r\n\r\n.default-icon-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n  color: #909399;\r\n  text-align: center;\r\n}\r\n\r\n.default-icon-placeholder i {\r\n  font-size: 12px;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.default-icon-placeholder span {\r\n  line-height: 1;\r\n}\r\n\r\n.icon-edit-btn {\r\n  padding: 4px !important;\r\n  min-height: auto !important;\r\n}\r\n\r\n/* 图标上传器样式 */\r\n.icon-uploader-content {\r\n  text-align: center;\r\n}\r\n\r\n.upload-section h4 {\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.upload-tips {\r\n  margin-top: 15px;\r\n  text-align: left;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.upload-tips p {\r\n  margin: 5px 0;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 预览区域图标样式 */\r\n.preview-module-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-module-icon {\r\n  font-size: 16px;\r\n  color: #409eff;\r\n}\r\n\r\n.preview-module-icon-img {\r\n  width: 16px;\r\n  height: 16px;\r\n  object-fit: cover;\r\n}\r\n\r\n.preview-dialog-module-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-dialog-module-icon {\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.preview-dialog-module-icon-img {\r\n  width: 18px;\r\n  height: 18px;\r\n  object-fit: cover;\r\n}\r\n\r\n/* 类型图标样式 */\r\n.category-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  object-fit: cover;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.no-icon {\r\n  color: #c0c4cc;\r\n  font-size: 12px;\r\n}\r\n\r\n.form-tip {\r\n  margin-top: 8px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.4;\r\n}\r\n\r\n.form-tip p {\r\n  margin: 2px 0;\r\n}\r\n\r\n.form-tip {\r\n  margin-top: 8px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.4;\r\n}\r\n\r\n.form-tip p {\r\n  margin: 2px 0;\r\n}\r\n\r\n</style>\r\n"]}]}