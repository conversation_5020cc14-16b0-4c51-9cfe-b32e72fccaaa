{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue", "mtime": 1754298434316}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_demandcategory", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "categoryList", "title", "open", "queryParams", "pageNum", "pageSize", "categoryName", "status", "form", "rules", "required", "message", "trigger", "categoryCode", "pattern", "formConfigOpen", "previewOpen", "currentCategoryId", "formFieldsList", "formModulesList", "selectedTemplate", "previewData", "previewDialogData", "iconUploaderOpen", "currentModuleIndex", "uploadedIcon", "created", "getList", "methods", "_this", "listDemandCategory", "then", "response", "rows", "cancel", "reset", "categoryId", "categoryShortName", "categoryIcon", "categoryDesc", "sortOrder", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getDemandCategory", "submitForm", "_this3", "$refs", "validate", "valid", "updateDemandCategory", "$modal", "msgSuccess", "addDemandCategory", "handleDelete", "_this4", "categoryIds", "confirm", "delDemandCategory", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleSortChange", "_this5", "handleFormConfig", "_this6", "formFields", "parsedData", "JSON", "parse", "Array", "isArray", "hasOwnProperty", "for<PERSON>ach", "module", "fields", "field", "hidden", "undefined", "$set", "description", "e", "console", "error", "initPreviewData", "_this7", "type", "addFormModule", "newModule", "icon", "push", "removeModule", "moduleIndex", "_this8", "$confirm", "confirmButtonText", "cancelButtonText", "splice", "$message", "success", "moveModuleUp", "temp", "moveModuleDown", "validateModuleName", "trim", "addFieldToModule", "_this9", "newField", "label", "options", "placeholder", "staticContent", "$nextTick", "handleIconCommand", "command", "openIconUploader", "confirmIconUpload", "cancelIconUpload", "onFieldTypeChange", "placeholderM<PERSON>", "onFieldHiddenChange", "removeFieldFromModule", "fieldIndex", "moveFieldUpInModule", "moveFieldDownInModule", "addFormField", "generateFieldName", "nameMap", "replace", "toLowerCase", "test", "now", "toString", "slice", "applyTemplate", "_this0", "applyBasicTemplate", "applyTechTemplate", "applyBusinessTemplate", "_this1", "_this10", "_this11", "getFieldOptions", "split", "option", "filter", "previewForm", "initPreviewDialogData", "_this12", "saveFormConfig", "_this13", "msgError", "i", "j", "formData", "stringify", "cancelFormConfig"], "sources": ["src/views/miniapp/business/demandcategory/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"类型名称\" prop=\"categoryName\">\r\n        <el-input\r\n          v-model=\"queryParams.categoryName\"\r\n          placeholder=\"请输入类型名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- 隐藏新增按钮 -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['miniapp:demandcategory:add']\"\r\n        >新增</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:demandcategory:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <!-- 隐藏删除按钮 -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:demandcategory:remove']\"\r\n        >删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:demandcategory:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"categoryList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      row-key=\"categoryId\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"类型ID\" align=\"center\" prop=\"categoryId\" width=\"100\" />\r\n      <el-table-column label=\"类型名称\" align=\"center\" prop=\"categoryName\" />\r\n      <el-table-column label=\"类型标识\" align=\"center\" prop=\"categoryCode\" width=\"120\" />\r\n      <el-table-column label=\"简称\" align=\"center\" prop=\"categoryShortName\" width=\"120\" />\r\n      <el-table-column label=\"图标\" align=\"center\" prop=\"categoryIcon\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <image-preview v-if=\"scope.row.categoryIcon\" :src=\"scope.row.categoryIcon\" :width=\"32\" :height=\"32\" />\r\n          <span v-else class=\"no-icon\">无图标</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"类型描述\" align=\"center\" prop=\"categoryDesc\" show-overflow-tooltip />\r\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input-number\r\n            v-model=\"scope.row.sortOrder\"\r\n            :min=\"0\"\r\n            size=\"mini\"\r\n            :controls=\"false\"\r\n            @change=\"handleSortChange(scope.row)\"\r\n            style=\"width: 80px;\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['miniapp:demandcategory:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-setting\"\r\n            @click=\"handleFormConfig(scope.row)\"\r\n            v-hasPermi=\"['miniapp:demandcategory:edit']\"\r\n          >表单配置</el-button>\r\n          <!-- 隐藏删除按钮 -->\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:demandcategory:remove']\"\r\n          >删除</el-button> -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改需求类型对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"类型名称\" prop=\"categoryName\">\r\n              <el-input v-model=\"form.categoryName\" placeholder=\"请输入类型名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"类型标识\" prop=\"categoryCode\">\r\n              <el-input\r\n                v-model=\"form.categoryCode\"\r\n                placeholder=\"请输入类型标识代码，如：tech、business\"\r\n                :disabled=\"form.categoryId != null\"\r\n              />\r\n              <div class=\"form-tip\">\r\n                <p>• 用于标识需求类型的唯一代码</p>\r\n                <p>• 建议格式：tech、business、service等</p>\r\n                <p v-if=\"form.categoryId == null\">• 一旦设置后不可修改</p>\r\n                <p v-else style=\"color: #f56c6c;\">• 类型标识不可修改</p>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"类型简称\" prop=\"categoryShortName\">\r\n              <el-input v-model=\"form.categoryShortName\" placeholder=\"请输入类型名称简称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n              <el-input-number v-model=\"form.sortOrder\" :min=\"0\" placeholder=\"数字越小越靠前\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"类型图标\" prop=\"categoryIcon\">\r\n              <ImageUpload\r\n                v-model=\"form.categoryIcon\"\r\n                :limit=\"1\"\r\n                :fileSize=\"2\"\r\n                :isShowTip=\"true\"\r\n              />\r\n              <div class=\"form-tip\">\r\n                <p>• 支持 jpg、png、gif 格式</p>\r\n                <p>• 文件大小不超过 2MB</p>\r\n                <p>• 建议尺寸 32x32 像素，保证清晰度</p>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"类型描述\" prop=\"categoryDesc\">\r\n              <Editor\r\n                v-model=\"form.categoryDesc\"\r\n                :min-height=\"200\"\r\n                :height=\"300\"\r\n                placeholder=\"请输入类型描述...\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" :rows=\"3\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单字段配置对话框 -->\r\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1400px\" append-to-body>\r\n      <div class=\"form-config-container\">\r\n        <!-- 左侧配置区域 -->\r\n        <div class=\"config-area\">\r\n          <div class=\"config-header\">\r\n            <h4>字段配置</h4>\r\n            <div class=\"header-actions\">\r\n              <el-button type=\"success\" size=\"small\" @click=\"addFormModule\" icon=\"el-icon-folder-add\">添加模块</el-button>\r\n              <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">添加字段</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 预设模板 -->\r\n          <div class=\"template-section\">\r\n            <el-select v-model=\"selectedTemplate\" placeholder=\"选择预设模板\" size=\"small\" @change=\"applyTemplate\">\r\n              <el-option label=\"基础需求模板\" value=\"basic\" />\r\n              <el-option label=\"技术需求模板\" value=\"tech\" />\r\n              <el-option label=\"商务合作模板\" value=\"business\" />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 模块和字段列表 -->\r\n          <div class=\"modules-list\">\r\n            <div v-for=\"(module, moduleIndex) in formModulesList\" :key=\"moduleIndex\" class=\"module-item\">\r\n              <el-card shadow=\"hover\" class=\"module-card\">\r\n                <!-- 模块头部 -->\r\n                <div class=\"module-header\">\r\n                  <div class=\"module-title-section\">\r\n                    <!-- 模块图标 -->\r\n                    <div class=\"module-icon-section\">\r\n                      <div class=\"module-icon-display\" @click=\"openIconUploader(moduleIndex)\">\r\n                        <img v-if=\"module.icon\" :src=\"module.icon\" class=\"custom-icon\" alt=\"模块图标\" />\r\n                        <div v-else class=\"default-icon-placeholder\">\r\n                          <i class=\"el-icon-plus\"></i>\r\n                          <span>上传图标</span>\r\n                        </div>\r\n                      </div>\r\n                      <el-dropdown trigger=\"click\" @command=\"handleIconCommand($event, moduleIndex)\" v-if=\"module.icon\">\r\n                        <el-button type=\"text\" size=\"mini\" class=\"icon-edit-btn\">\r\n                          <i class=\"el-icon-edit\"></i>\r\n                        </el-button>\r\n                        <el-dropdown-menu slot=\"dropdown\">\r\n                          <el-dropdown-item command=\"upload\">重新上传</el-dropdown-item>\r\n                          <el-dropdown-item command=\"remove\">移除图标</el-dropdown-item>\r\n                        </el-dropdown-menu>\r\n                      </el-dropdown>\r\n                    </div>\r\n\r\n                    <el-input\r\n                      v-model=\"module.name\"\r\n                      placeholder=\"请输入模块名称\"\r\n                      size=\"small\"\r\n                      class=\"module-name-input\"\r\n                      @blur=\"validateModuleName(module)\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"module-actions\">\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"moveModuleUp(moduleIndex)\" :disabled=\"moduleIndex === 0\">上移</el-button>\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"moveModuleDown(moduleIndex)\" :disabled=\"moduleIndex === formModulesList.length - 1\">下移</el-button>\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"addFieldToModule(moduleIndex)\" icon=\"el-icon-plus\">添加字段</el-button>\r\n                    <el-button type=\"text\" size=\"mini\" @click=\"removeModule(moduleIndex)\" style=\"color: #f56c6c;\">删除模块</el-button>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n\r\n                <!-- 字段列表 -->\r\n                <div class=\"fields-list\" v-if=\"module.fields && module.fields.length > 0\">\r\n                  <div v-for=\"(field, fieldIndex) in module.fields\" :key=\"fieldIndex\" class=\"field-item\">\r\n                    <el-card shadow=\"hover\" class=\"field-card\">\r\n                      <div class=\"field-header\">\r\n                        <span class=\"field-title\">\r\n                          {{ field.label || '未命名字段' }}\r\n                          <el-tag v-if=\"field.hidden\" size=\"mini\" type=\"info\" style=\"margin-left: 8px;\">隐藏</el-tag>\r\n                        </span>\r\n                        <div class=\"field-actions\">\r\n                          <el-button type=\"text\" size=\"mini\" @click=\"moveFieldUpInModule(moduleIndex, fieldIndex)\" :disabled=\"fieldIndex === 0\">上移</el-button>\r\n                          <el-button type=\"text\" size=\"mini\" @click=\"moveFieldDownInModule(moduleIndex, fieldIndex)\" :disabled=\"fieldIndex === module.fields.length - 1\">下移</el-button>\r\n                          <el-button type=\"text\" size=\"mini\" @click=\"removeFieldFromModule(moduleIndex, fieldIndex)\" style=\"color: #f56c6c;\">删除</el-button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <el-form :model=\"field\" label-width=\"80px\" size=\"small\">\r\n                        <el-form-item label=\"字段标签\">\r\n                          <el-input v-model=\"field.label\" placeholder=\"请输入字段标签\" @input=\"generateFieldName(field)\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"字段类型\">\r\n                          <el-select v-model=\"field.type\" placeholder=\"请选择字段类型\" @change=\"onFieldTypeChange(field)\">\r\n                            <el-option label=\"文本输入\" value=\"input\" />\r\n                            <el-option label=\"多行文本\" value=\"textarea\" />\r\n                            <el-option label=\"数字输入\" value=\"number\" />\r\n                            <el-option label=\"电话号码\" value=\"tel\" />\r\n                            <el-option label=\"邮箱地址\" value=\"email\" />\r\n                            <el-option label=\"单选框\" value=\"radio\" />\r\n                            <el-option label=\"多选框\" value=\"checkbox\" />\r\n                            <el-option label=\"下拉选择\" value=\"select\" />\r\n                            <el-option label=\"日期选择\" value=\"date\" />\r\n                            <el-option label=\"时间选择\" value=\"time\" />\r\n                            <el-option label=\"文件上传\" value=\"file\" />\r\n                            <el-option label=\"静态展示\" value=\"static\" />\r\n                          </el-select>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"提示文字\" v-if=\"field.type !== 'static'\">\r\n                          <el-input v-model=\"field.placeholder\" placeholder=\"请输入提示文字，如：请输入姓名\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"显示内容\" v-if=\"field.type === 'static'\">\r\n                          <el-input v-model=\"field.staticContent\" type=\"textarea\" placeholder=\"请输入要显示的静态内容\" :rows=\"3\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"是否必填\" v-if=\"field.type !== 'static'\">\r\n                          <el-switch v-model=\"field.required\" />\r\n                        </el-form-item>\r\n                        <el-form-item label=\"是否隐藏\">\r\n                          <el-switch v-model=\"field.hidden\" @change=\"onFieldHiddenChange(field)\" />\r\n                          <div class=\"field-tip\" v-if=\"field.hidden\">\r\n                            <i class=\"el-icon-info\"></i>\r\n                            <span>隐藏的字段在小程序端不会显示给用户</span>\r\n                          </div>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"选项配置\" v-if=\"['radio', 'checkbox', 'select'].includes(field.type)\">\r\n                          <el-input v-model=\"field.options\" type=\"textarea\" placeholder=\"请输入选项，用逗号分隔\" :rows=\"2\" />\r\n                        </el-form-item>\r\n                      </el-form>\r\n                    </el-card>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 空模块提示 -->\r\n                <div v-else class=\"empty-module\">\r\n                  <el-empty description=\"暂无字段\" :image-size=\"60\">\r\n                    <el-button type=\"primary\" size=\"small\" @click=\"addFieldToModule(moduleIndex)\">添加字段</el-button>\r\n                  </el-empty>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"formModulesList.length === 0\" class=\"empty-modules\">\r\n              <el-empty description=\"暂无模块\" :image-size=\"80\">\r\n                <el-button type=\"primary\" @click=\"addFormModule\">创建第一个模块</el-button>\r\n              </el-empty>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧预览区域 -->\r\n        <div class=\"preview-area\">\r\n          <div class=\"preview-header\">\r\n            <h4>表单预览</h4>\r\n            <el-button type=\"success\" size=\"small\" @click=\"previewForm\">预览表单</el-button>\r\n          </div>\r\n          <div class=\"preview-content\">\r\n            <div v-for=\"(module, moduleIndex) in formModulesList\" :key=\"moduleIndex\" class=\"preview-module\">\r\n              <div class=\"preview-module-header\">\r\n                <div class=\"preview-module-title\">\r\n                  <img v-if=\"module.icon\" :src=\"module.icon\" class=\"preview-module-icon-img\" alt=\"模块图标\" />\r\n                  <i v-else class=\"el-icon-folder-opened preview-module-icon\"></i>\r\n                  <h5>{{ module.name || '未命名模块' }}</h5>\r\n                </div>\r\n              </div>\r\n              <el-form label-width=\"100px\" size=\"small\" v-if=\"module.fields && module.fields.length > 0\">\r\n                <el-form-item v-for=\"field in module.fields\" :key=\"field.name\" :label=\"field.label\" :required=\"field.required && field.type !== 'static'\" :class=\"{ 'hidden-field': field.hidden }\">\r\n                  <div v-if=\"field.hidden\" class=\"hidden-field-indicator\">\r\n                    <el-tag size=\"mini\" type=\"info\">隐藏字段</el-tag>\r\n                  </div>\r\n                  <!-- 静态展示 -->\r\n                  <div v-if=\"field.type === 'static'\" class=\"static-content\">\r\n                    {{ field.staticContent || '暂无内容' }}\r\n                  </div>\r\n                  <!-- 文本输入 -->\r\n                  <el-input v-else-if=\"field.type === 'input'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入'\" disabled />\r\n                  <!-- 多行文本 -->\r\n                  <el-input v-else-if=\"field.type === 'textarea'\" v-model=\"previewData[field.name]\" type=\"textarea\" :placeholder=\"field.placeholder || '请输入'\" disabled />\r\n                  <!-- 数字输入 -->\r\n                  <el-input-number v-else-if=\"field.type === 'number'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入数字'\" disabled />\r\n                  <!-- 电话号码 -->\r\n                  <el-input v-else-if=\"field.type === 'tel'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入电话号码'\" disabled />\r\n                  <!-- 邮箱地址 -->\r\n                  <el-input v-else-if=\"field.type === 'email'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请输入邮箱地址'\" disabled />\r\n                  <!-- 单选框 -->\r\n                  <el-radio-group v-else-if=\"field.type === 'radio'\" v-model=\"previewData[field.name]\" disabled>\r\n                    <el-radio v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-radio>\r\n                  </el-radio-group>\r\n                  <!-- 多选框 -->\r\n                  <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" v-model=\"previewData[field.name]\" disabled>\r\n                    <el-checkbox v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-checkbox>\r\n                  </el-checkbox-group>\r\n                  <!-- 下拉选择 -->\r\n                  <el-select v-else-if=\"field.type === 'select'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请选择'\" disabled>\r\n                    <el-option v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\" :value=\"option\" />\r\n                  </el-select>\r\n                  <!-- 日期选择 -->\r\n                  <el-date-picker v-else-if=\"field.type === 'date'\" v-model=\"previewData[field.name]\" type=\"date\" :placeholder=\"field.placeholder || '请选择日期'\" disabled />\r\n                  <!-- 时间选择 -->\r\n                  <el-time-picker v-else-if=\"field.type === 'time'\" v-model=\"previewData[field.name]\" :placeholder=\"field.placeholder || '请选择时间'\" disabled />\r\n                  <!-- 文件上传 -->\r\n                  <el-upload v-else-if=\"field.type === 'file'\" action=\"#\" disabled>\r\n                    <el-button size=\"small\" type=\"primary\" disabled>{{ field.placeholder || '点击上传' }}</el-button>\r\n                  </el-upload>\r\n                </el-form-item>\r\n              </el-form>\r\n              <div v-else class=\"preview-empty-module\">\r\n                <span>该模块暂无字段</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空状态 -->\r\n            <div v-if=\"formModulesList.length === 0\" class=\"preview-empty\">\r\n              <el-empty description=\"暂无表单配置\" :image-size=\"60\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\r\n        <el-button @click=\"cancelFormConfig\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewOpen\" width=\"800px\" append-to-body>\r\n      <div class=\"preview-dialog-content\">\r\n        <div v-for=\"(module, moduleIndex) in formModulesList\" :key=\"moduleIndex\" class=\"preview-dialog-module\">\r\n          <div class=\"preview-dialog-module-header\">\r\n            <div class=\"preview-dialog-module-title\">\r\n              <img v-if=\"module.icon\" :src=\"module.icon\" class=\"preview-dialog-module-icon-img\" alt=\"模块图标\" />\r\n              <i v-else class=\"el-icon-folder-opened preview-dialog-module-icon\"></i>\r\n              <h4>{{ module.name || '未命名模块' }}</h4>\r\n            </div>\r\n          </div>\r\n          <el-form label-width=\"100px\" v-if=\"module.fields && module.fields.length > 0\">\r\n            <el-form-item v-for=\"field in module.fields\" :key=\"field.name\" :label=\"field.label\" :required=\"field.required && field.type !== 'static'\" :class=\"{ 'hidden-field': field.hidden }\">\r\n              <div v-if=\"field.hidden\" class=\"hidden-field-indicator\">\r\n                <el-tag size=\"mini\" type=\"info\">隐藏字段</el-tag>\r\n              </div>\r\n              <!-- 静态展示 -->\r\n              <div v-if=\"field.type === 'static'\" class=\"static-content\">\r\n                {{ field.staticContent || '暂无内容' }}\r\n              </div>\r\n              <!-- 文本输入 -->\r\n              <el-input v-else-if=\"field.type === 'input'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入'\" />\r\n              <!-- 多行文本 -->\r\n              <el-input v-else-if=\"field.type === 'textarea'\" v-model=\"previewDialogData[field.name]\" type=\"textarea\" :placeholder=\"field.placeholder || '请输入'\" />\r\n              <!-- 数字输入 -->\r\n              <el-input-number v-else-if=\"field.type === 'number'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入数字'\" />\r\n              <!-- 电话号码 -->\r\n              <el-input v-else-if=\"field.type === 'tel'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入电话号码'\" />\r\n              <!-- 邮箱地址 -->\r\n              <el-input v-else-if=\"field.type === 'email'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请输入邮箱地址'\" />\r\n              <!-- 单选框 -->\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" v-model=\"previewDialogData[field.name]\">\r\n                <el-radio v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-radio>\r\n              </el-radio-group>\r\n              <!-- 多选框 -->\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" v-model=\"previewDialogData[field.name]\">\r\n                <el-checkbox v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\">{{ option }}</el-checkbox>\r\n              </el-checkbox-group>\r\n              <!-- 下拉选择 -->\r\n              <el-select v-else-if=\"field.type === 'select'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请选择'\">\r\n                <el-option v-for=\"option in getFieldOptions(field)\" :key=\"option\" :label=\"option\" :value=\"option\" />\r\n              </el-select>\r\n              <!-- 日期选择 -->\r\n              <el-date-picker v-else-if=\"field.type === 'date'\" v-model=\"previewDialogData[field.name]\" type=\"date\" :placeholder=\"field.placeholder || '请选择日期'\" />\r\n              <!-- 时间选择 -->\r\n              <el-time-picker v-else-if=\"field.type === 'time'\" v-model=\"previewDialogData[field.name]\" :placeholder=\"field.placeholder || '请选择时间'\" />\r\n              <!-- 文件上传 -->\r\n              <el-upload v-else-if=\"field.type === 'file'\" action=\"#\">\r\n                <el-button size=\"small\" type=\"primary\">{{ field.placeholder || '点击上传' }}</el-button>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-form>\r\n          <div v-else class=\"preview-dialog-empty-module\">\r\n            <span>该模块暂无字段</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空状态 -->\r\n        <div v-if=\"formModulesList.length === 0\" class=\"preview-dialog-empty\">\r\n          <el-empty description=\"暂无表单配置\" :image-size=\"60\" />\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 图标上传对话框 -->\r\n    <el-dialog title=\"上传模块图标\" :visible.sync=\"iconUploaderOpen\" width=\"500px\" append-to-body>\r\n      <div class=\"icon-uploader-content\">\r\n        <div class=\"upload-section\">\r\n          <h4>上传自定义图标</h4>\r\n          <ImageUpload\r\n            v-model=\"uploadedIcon\"\r\n            :limit=\"1\"\r\n            :fileSize=\"2\"\r\n            :isShowTip=\"true\"\r\n          />\r\n          <div class=\"upload-tips\">\r\n            <p>• 支持 jpg、png、gif 格式</p>\r\n            <p>• 文件大小不超过 2MB</p>\r\n            <p>• 建议尺寸 32x32 像素，保证清晰度</p>\r\n            <p>• 建议使用透明背景的PNG格式</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelIconUpload\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmIconUpload\" :disabled=\"!uploadedIcon\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDemandCategory, getDemandCategory, delDemandCategory, addDemandCategory, updateDemandCategory } from \"@/api/miniapp/demandcategory\";\r\n\r\nexport default {\r\n  name: \"MiniDemandCategory\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 需求类型表格数据\r\n      categoryList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        categoryName: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        categoryName: [\r\n          { required: true, message: \"类型名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        categoryCode: [\r\n          { required: true, message: \"类型标识不能为空\", trigger: \"blur\" },\r\n          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: \"类型标识必须以字母开头，只能包含字母、数字和下划线\", trigger: \"blur\" }\r\n        ],\r\n        status: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 表单配置相关\r\n      formConfigOpen: false,\r\n      previewOpen: false,\r\n      currentCategoryId: null,\r\n      formFieldsList: [], // 保留兼容性\r\n      formModulesList: [], // 新的模块化结构\r\n      selectedTemplate: '',\r\n      previewData: {}, // 预览区域的表单数据\r\n      previewDialogData: {}, // 预览对话框的表单数据\r\n      // 图标相关\r\n      iconUploaderOpen: false,\r\n      currentModuleIndex: -1,\r\n      uploadedIcon: ''\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n\r\n    /** 查询需求类型列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDemandCategory(this.queryParams).then(response => {\r\n        this.categoryList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        categoryId: null,\r\n        categoryName: null,\r\n        categoryCode: null,\r\n        categoryShortName: null,\r\n        categoryIcon: null,\r\n        categoryDesc: null,\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.categoryId);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加需求类型\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const categoryId = row.categoryId || this.ids;\r\n      getDemandCategory(categoryId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改需求类型\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.categoryId != null) {\r\n            updateDemandCategory(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDemandCategory(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const categoryIds = row.categoryId || this.ids;\r\n      this.$modal.confirm('是否确认删除需求类型编号为\"' + categoryIds + '\"的数据项？').then(function() {\r\n        return delDemandCategory(categoryIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/demandcategory/export', {\r\n        ...this.queryParams\r\n      }, `需求类型数据_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 排序修改 */\r\n    handleSortChange(row) {\r\n      updateDemandCategory(row).then(response => {\r\n        this.$modal.msgSuccess(\"排序修改成功\");\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig(row) {\r\n      this.currentCategoryId = row.categoryId;\r\n      this.formFieldsList = [];\r\n      this.formModulesList = [];\r\n\r\n      // 如果已有配置，解析JSON\r\n      if (row.formFields) {\r\n        try {\r\n          const parsedData = JSON.parse(row.formFields);\r\n\r\n          // 检查是否为新的模块化结构\r\n          if (Array.isArray(parsedData) && parsedData.length > 0 && parsedData[0].hasOwnProperty('name')) {\r\n            // 新的模块化结构\r\n            this.formModulesList = parsedData;\r\n            // 确保所有字段都有 hidden 属性\r\n            this.formModulesList.forEach(module => {\r\n              if (module.fields) {\r\n                module.fields.forEach(field => {\r\n                  if (field.hidden === undefined) {\r\n                    this.$set(field, 'hidden', false);\r\n                  }\r\n                });\r\n              }\r\n            });\r\n          } else if (Array.isArray(parsedData)) {\r\n            // 旧的字段列表结构，转换为模块化结构\r\n            this.formFieldsList = parsedData;\r\n            if (parsedData.length > 0) {\r\n              // 为旧字段添加 hidden 属性\r\n              parsedData.forEach(field => {\r\n                if (field.hidden === undefined) {\r\n                  this.$set(field, 'hidden', false);\r\n                }\r\n              });\r\n              this.formModulesList = [{\r\n                name: '基础信息',\r\n                description: '',\r\n                fields: parsedData\r\n              }];\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error('解析表单配置失败:', e);\r\n          this.formFieldsList = [];\r\n          this.formModulesList = [];\r\n        }\r\n      }\r\n\r\n      this.formConfigOpen = true;\r\n      this.initPreviewData();\r\n    },\r\n\r\n    /** 初始化预览数据 */\r\n    initPreviewData() {\r\n      this.previewData = {};\r\n      this.formModulesList.forEach(module => {\r\n        if (module.fields) {\r\n          module.fields.forEach(field => {\r\n            if (field.name) {\r\n              if (field.type === 'checkbox') {\r\n                this.previewData[field.name] = [];\r\n              } else if (field.type === 'number') {\r\n                this.previewData[field.name] = null;\r\n              } else if (field.type === 'date' || field.type === 'time') {\r\n                this.previewData[field.name] = null;\r\n              } else {\r\n                this.previewData[field.name] = '';\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 添加表单模块 */\r\n    addFormModule() {\r\n      const newModule = {\r\n        name: '新模块',\r\n        description: '',\r\n        fields: [],\r\n        icon: '' // 默认无图标，需要用户上传\r\n      };\r\n      this.formModulesList.push(newModule);\r\n    },\r\n\r\n    /** 删除模块 */\r\n    removeModule(moduleIndex) {\r\n      this.$confirm('确认删除该模块及其所有字段吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.formModulesList.splice(moduleIndex, 1);\r\n        this.$message.success('删除成功');\r\n      }).catch(() => {});\r\n    },\r\n\r\n    /** 模块上移 */\r\n    moveModuleUp(moduleIndex) {\r\n      if (moduleIndex > 0) {\r\n        const temp = this.formModulesList[moduleIndex];\r\n        this.$set(this.formModulesList, moduleIndex, this.formModulesList[moduleIndex - 1]);\r\n        this.$set(this.formModulesList, moduleIndex - 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 模块下移 */\r\n    moveModuleDown(moduleIndex) {\r\n      if (moduleIndex < this.formModulesList.length - 1) {\r\n        const temp = this.formModulesList[moduleIndex];\r\n        this.$set(this.formModulesList, moduleIndex, this.formModulesList[moduleIndex + 1]);\r\n        this.$set(this.formModulesList, moduleIndex + 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 验证模块名称 */\r\n    validateModuleName(module) {\r\n      if (!module.name || module.name.trim() === '') {\r\n        module.name = '未命名模块';\r\n      }\r\n    },\r\n\r\n    /** 向模块添加字段 */\r\n    addFieldToModule(moduleIndex) {\r\n      const newField = {\r\n        label: '',\r\n        name: '',\r\n        type: 'input',\r\n        required: false,\r\n        hidden: false, // 默认不隐藏\r\n        options: '',\r\n        placeholder: '请输入',\r\n        staticContent: ''\r\n      };\r\n      if (!this.formModulesList[moduleIndex].fields) {\r\n        this.$set(this.formModulesList[moduleIndex], 'fields', []);\r\n      }\r\n      this.formModulesList[moduleIndex].fields.push(newField);\r\n      // 更新预览数据\r\n      this.$nextTick(() => {\r\n        this.initPreviewData();\r\n      });\r\n    },\r\n\r\n    /** 处理图标命令 */\r\n    handleIconCommand(command, moduleIndex) {\r\n      this.currentModuleIndex = moduleIndex;\r\n\r\n      if (command === 'upload') {\r\n        this.uploadedIcon = '';\r\n        this.iconUploaderOpen = true;\r\n      } else if (command === 'remove') {\r\n        this.$set(this.formModulesList[moduleIndex], 'icon', '');\r\n      }\r\n    },\r\n\r\n    /** 打开图标上传器 */\r\n    openIconUploader(moduleIndex) {\r\n      this.currentModuleIndex = moduleIndex;\r\n      this.uploadedIcon = '';\r\n      this.iconUploaderOpen = true;\r\n    },\r\n\r\n    /** 确认图标上传 */\r\n    confirmIconUpload() {\r\n      if (this.currentModuleIndex >= 0 && this.uploadedIcon) {\r\n        this.$set(this.formModulesList[this.currentModuleIndex], 'icon', this.uploadedIcon);\r\n        this.$message.success('图标上传成功');\r\n      }\r\n      this.iconUploaderOpen = false;\r\n      this.currentModuleIndex = -1;\r\n      this.uploadedIcon = '';\r\n    },\r\n\r\n    /** 取消图标上传 */\r\n    cancelIconUpload() {\r\n      this.iconUploaderOpen = false;\r\n      this.currentModuleIndex = -1;\r\n      this.uploadedIcon = '';\r\n    },\r\n\r\n    /** 字段类型变化时的处理 */\r\n    onFieldTypeChange(field) {\r\n      // 根据字段类型设置默认的placeholder\r\n      const placeholderMap = {\r\n        'input': '请输入',\r\n        'textarea': '请输入',\r\n        'number': '请输入数字',\r\n        'tel': '请输入电话号码',\r\n        'email': '请输入邮箱地址',\r\n        'radio': '',\r\n        'checkbox': '',\r\n        'select': '请选择',\r\n        'date': '请选择日期',\r\n        'time': '请选择时间',\r\n        'file': '点击上传',\r\n        'static': ''\r\n      };\r\n\r\n      if (!field.placeholder || field.placeholder === '') {\r\n        field.placeholder = placeholderMap[field.type] || '请输入';\r\n      }\r\n\r\n      // 如果是静态展示字段，设置默认内容和清除必填状态\r\n      if (field.type === 'static') {\r\n        field.required = false;\r\n        if (!field.staticContent) {\r\n          field.staticContent = '这里是静态展示内容';\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 字段隐藏状态变化时的处理 */\r\n    onFieldHiddenChange() {\r\n      // 触发视图更新，无需额外处理\r\n    },\r\n\r\n    /** 从模块删除字段 */\r\n    removeFieldFromModule(moduleIndex, fieldIndex) {\r\n      this.formModulesList[moduleIndex].fields.splice(fieldIndex, 1);\r\n    },\r\n\r\n    /** 模块内字段上移 */\r\n    moveFieldUpInModule(moduleIndex, fieldIndex) {\r\n      const fields = this.formModulesList[moduleIndex].fields;\r\n      if (fieldIndex > 0) {\r\n        const temp = fields[fieldIndex];\r\n        this.$set(fields, fieldIndex, fields[fieldIndex - 1]);\r\n        this.$set(fields, fieldIndex - 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 模块内字段下移 */\r\n    moveFieldDownInModule(moduleIndex, fieldIndex) {\r\n      const fields = this.formModulesList[moduleIndex].fields;\r\n      if (fieldIndex < fields.length - 1) {\r\n        const temp = fields[fieldIndex];\r\n        this.$set(fields, fieldIndex, fields[fieldIndex + 1]);\r\n        this.$set(fields, fieldIndex + 1, temp);\r\n      }\r\n    },\r\n\r\n    /** 添加表单字段（兼容旧方法） */\r\n    addFormField() {\r\n      // 如果没有模块，先创建一个默认模块\r\n      if (this.formModulesList.length === 0) {\r\n        this.addFormModule();\r\n        this.formModulesList[0].name = '基础信息';\r\n      }\r\n\r\n      // 添加到第一个模块\r\n      this.addFieldToModule(0);\r\n    },\r\n\r\n\r\n\r\n    /** 生成字段名称 */\r\n    generateFieldName(field) {\r\n      if (field.label) {\r\n        // 扩展的中文转英文映射\r\n        const nameMap = {\r\n          // 基础信息\r\n          '姓名': 'name',\r\n          '联系人': 'contact_name',\r\n          '联系电话': 'phone',\r\n          '手机号': 'phone',\r\n          '电话': 'phone',\r\n          '邮箱': 'email',\r\n          '邮箱地址': 'email',\r\n          '公司': 'company',\r\n          '公司名称': 'company_name',\r\n          '职位': 'position',\r\n          '部门': 'department',\r\n          '地址': 'address',\r\n          '详细地址': 'detailed_address',\r\n\r\n          // 技术相关\r\n          '技术方向': 'tech_direction',\r\n          '技术栈': 'tech_stack',\r\n          '开发语言': 'programming_language',\r\n          '项目周期': 'project_duration',\r\n          '预算范围': 'budget_range',\r\n          '项目详细需求': 'detailed_requirements',\r\n          '技术要求': 'tech_requirements',\r\n\r\n          // 市场推广相关\r\n          '推广类型': 'promotion_type',\r\n          '目标客户群体': 'target_audience',\r\n          '推广渠道': 'promotion_channels',\r\n          '推广预算': 'promotion_budget',\r\n          '推广时间': 'promotion_duration',\r\n          '推广目标': 'promotion_goals',\r\n\r\n          // 招聘相关\r\n          '招聘职位': 'job_position',\r\n          '工作经验': 'work_experience',\r\n          '学历要求': 'education_requirement',\r\n          '薪资范围': 'salary_range',\r\n          '工作地点': 'work_location',\r\n          '职位描述': 'job_description',\r\n\r\n          // 投资相关\r\n          '投资类型': 'investment_type',\r\n          '投资金额': 'investment_amount',\r\n          '投资阶段': 'investment_stage',\r\n          '行业领域': 'industry_field',\r\n          '项目介绍': 'project_introduction',\r\n\r\n          // 采购相关\r\n          '产品名称': 'product_name',\r\n          '采购数量': 'purchase_quantity',\r\n          '质量要求': 'quality_requirements',\r\n          '交付时间': 'delivery_time',\r\n          '采购预算': 'purchase_budget',\r\n\r\n          // 通用字段\r\n          '需求描述': 'description',\r\n          '详细说明': 'detailed_description',\r\n          '备注': 'remark',\r\n          '说明': 'note',\r\n          '标题': 'title',\r\n          '内容': 'content',\r\n          '时间': 'time',\r\n          '日期': 'date',\r\n          '文件': 'file',\r\n          '图片': 'image',\r\n          '附件': 'attachment'\r\n        };\r\n\r\n        // 如果有直接映射，使用映射值\r\n        if (nameMap[field.label]) {\r\n          field.name = nameMap[field.label];\r\n        } else {\r\n          // 否则进行智能转换\r\n          let name = field.label\r\n            .replace(/[\\s\\-\\/\\\\]/g, '_') // 替换空格、横线、斜线为下划线\r\n            .replace(/[^\\w\\u4e00-\\u9fa5]/g, '') // 移除特殊字符，保留中英文和数字\r\n            .toLowerCase();\r\n\r\n          // 如果包含中文，尝试转换为拼音或英文\r\n          if (/[\\u4e00-\\u9fa5]/.test(name)) {\r\n            // 简单的中文关键词替换\r\n            name = name\r\n              .replace(/类型/g, 'type')\r\n              .replace(/名称/g, 'name')\r\n              .replace(/时间/g, 'time')\r\n              .replace(/日期/g, 'date')\r\n              .replace(/地址/g, 'address')\r\n              .replace(/电话/g, 'phone')\r\n              .replace(/邮箱/g, 'email')\r\n              .replace(/公司/g, 'company')\r\n              .replace(/描述/g, 'description')\r\n              .replace(/说明/g, 'note')\r\n              .replace(/备注/g, 'remark')\r\n              .replace(/要求/g, 'requirement')\r\n              .replace(/范围/g, 'range')\r\n              .replace(/预算/g, 'budget')\r\n              .replace(/数量/g, 'quantity')\r\n              .replace(/价格/g, 'price')\r\n              .replace(/费用/g, 'cost');\r\n\r\n            // 如果还有中文，使用拼音首字母或保持原样\r\n            if (/[\\u4e00-\\u9fa5]/.test(name)) {\r\n              name = 'field_' + Date.now().toString().slice(-6); // 使用时间戳后6位作为唯一标识\r\n            }\r\n          }\r\n\r\n          field.name = name;\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 应用预设模板 */\r\n    applyTemplate() {\r\n      if (this.selectedTemplate === 'basic') {\r\n        this.applyBasicTemplate();\r\n      } else if (this.selectedTemplate === 'tech') {\r\n        this.applyTechTemplate();\r\n      } else if (this.selectedTemplate === 'business') {\r\n        this.applyBusinessTemplate();\r\n      }\r\n      // 应用模板后初始化预览数据\r\n      this.$nextTick(() => {\r\n        this.initPreviewData();\r\n      });\r\n    },\r\n\r\n    /** 应用基础需求模板 */\r\n    applyBasicTemplate() {\r\n      this.formModulesList = [\r\n        {\r\n          name: '填写说明',\r\n          description: '请仔细阅读以下说明后填写表单',\r\n          fields: [\r\n            {\r\n              label: '温馨提示',\r\n              name: 'tips',\r\n              type: 'static',\r\n              required: false,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '',\r\n              staticContent: '请如实填写以下信息，我们将在24小时内与您取得联系。带*号的为必填项。'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '基础信息',\r\n          description: '请填写需求的基本信息',\r\n          fields: [\r\n            {\r\n              label: '需求标题',\r\n              name: 'title',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入需求标题',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '需求描述',\r\n              name: 'description',\r\n              type: 'textarea',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请详细描述您的需求',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '联系方式',\r\n          description: '请填写您的联系方式，以便我们与您取得联系',\r\n          fields: [\r\n            {\r\n              label: '联系人',\r\n              name: 'contact_name',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入联系人姓名',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '联系电话',\r\n              name: 'phone',\r\n              type: 'tel',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入手机号码',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 确保字段名称正确生成\r\n      this.formModulesList.forEach(module => {\r\n        module.fields.forEach(field => {\r\n          this.generateFieldName(field);\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 应用技术需求模板 */\r\n    applyTechTemplate() {\r\n      this.formModulesList = [\r\n        {\r\n          name: '技术需求',\r\n          description: '请详细描述您的技术需求',\r\n          fields: [\r\n            {\r\n              label: '技术方向',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '前端开发,后端开发,移动开发,人工智能,大数据,云计算',\r\n              placeholder: '请选择技术方向',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '技术栈',\r\n              name: '',\r\n              type: 'checkbox',\r\n              required: false,\r\n              hidden: false,\r\n              options: 'Java,Python,JavaScript,React,Vue,Spring Boot,MySQL,Redis',\r\n              placeholder: '',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '项目详细需求',\r\n              name: '',\r\n              type: 'textarea',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请详细描述项目需求、功能要求、技术要求等',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '项目信息',\r\n          description: '请填写项目的基本信息',\r\n          fields: [\r\n            {\r\n              label: '项目周期',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '1周内,1-2周,2-4周,1-2个月,2-3个月,3个月以上',\r\n              placeholder: '请选择项目周期',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '预算范围',\r\n              name: '',\r\n              type: 'radio',\r\n              required: true,\r\n              hidden: false,\r\n              options: '1万以下,1-5万,5-10万,10-20万,20万以上',\r\n              placeholder: '',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '联系方式',\r\n          description: '请填写您的联系方式',\r\n          fields: [\r\n            {\r\n              label: '联系人',\r\n              name: '',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入联系人姓名',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '联系电话',\r\n              name: '',\r\n              type: 'tel',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入手机号码',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 自动生成字段名称\r\n      this.formModulesList.forEach(module => {\r\n        module.fields.forEach(field => {\r\n          this.generateFieldName(field);\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 应用商务合作模板 */\r\n    applyBusinessTemplate() {\r\n      this.formModulesList = [\r\n        {\r\n          name: '合作信息',\r\n          description: '请填写合作相关信息',\r\n          fields: [\r\n            {\r\n              label: '合作类型',\r\n              name: '',\r\n              type: 'radio',\r\n              required: true,\r\n              hidden: false,\r\n              options: '战略合作,技术合作,市场合作,投资合作',\r\n              placeholder: '',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '合作描述',\r\n              name: '',\r\n              type: 'textarea',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请详细描述合作内容、合作方式、期望达成的目标等',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '公司信息',\r\n          description: '请填写您的公司基本信息',\r\n          fields: [\r\n            {\r\n              label: '公司名称',\r\n              name: '',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入公司全称',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '公司规模',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '10人以下,10-50人,50-200人,200-500人,500人以上',\r\n              placeholder: '请选择公司规模',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '行业领域',\r\n              name: '',\r\n              type: 'select',\r\n              required: true,\r\n              hidden: false,\r\n              options: '互联网,金融,教育,医疗,制造业,服务业,其他',\r\n              placeholder: '请选择行业领域',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: '联系方式',\r\n          description: '请填写联系方式，以便我们与您取得联系',\r\n          fields: [\r\n            {\r\n              label: '联系人',\r\n              name: '',\r\n              type: 'input',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入联系人姓名',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '联系电话',\r\n              name: '',\r\n              type: 'tel',\r\n              required: true,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入手机号码',\r\n              staticContent: ''\r\n            },\r\n            {\r\n              label: '邮箱地址',\r\n              name: '',\r\n              type: 'email',\r\n              required: false,\r\n              hidden: false,\r\n              options: '',\r\n              placeholder: '请输入邮箱地址（选填）',\r\n              staticContent: ''\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // 自动生成字段名称\r\n      this.formModulesList.forEach(module => {\r\n        module.fields.forEach(field => {\r\n          this.generateFieldName(field);\r\n        });\r\n      });\r\n    },\r\n\r\n    /** 获取字段选项 */\r\n    getFieldOptions(field) {\r\n      if (!field.options || field.options.trim() === '') return [];\r\n      return field.options.split(',').map(option => option.trim()).filter(option => option !== '');\r\n    },\r\n\r\n    /** 预览表单 */\r\n    previewForm() {\r\n      this.initPreviewDialogData();\r\n      this.previewOpen = true;\r\n    },\r\n\r\n    /** 初始化预览对话框数据 */\r\n    initPreviewDialogData() {\r\n      this.previewDialogData = {};\r\n      this.formModulesList.forEach(module => {\r\n        if (module.fields) {\r\n          module.fields.forEach(field => {\r\n            if (field.name) {\r\n              if (field.type === 'checkbox') {\r\n                this.previewDialogData[field.name] = [];\r\n              } else if (field.type === 'number') {\r\n                this.previewDialogData[field.name] = null;\r\n              } else if (field.type === 'date' || field.type === 'time') {\r\n                this.previewDialogData[field.name] = null;\r\n              } else {\r\n                this.previewDialogData[field.name] = '';\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (!this.currentCategoryId) {\r\n        this.$modal.msgError(\"未选择需求类型\");\r\n        return;\r\n      }\r\n\r\n      // 验证模块配置\r\n      for (let i = 0; i < this.formModulesList.length; i++) {\r\n        const module = this.formModulesList[i];\r\n        if (!module.name || module.name.trim() === '') {\r\n          this.$modal.msgError(`第${i + 1}个模块名称不能为空`);\r\n          return;\r\n        }\r\n\r\n        for (let j = 0; j < module.fields.length; j++) {\r\n          const field = module.fields[j];\r\n          if (!field.label || field.label.trim() === '') {\r\n            this.$modal.msgError(`模块\"${module.name}\"中第${j + 1}个字段标签不能为空`);\r\n            return;\r\n          }\r\n        }\r\n      }\r\n\r\n      const formData = {\r\n        categoryId: this.currentCategoryId,\r\n        formFields: JSON.stringify(this.formModulesList)\r\n      };\r\n\r\n      updateDemandCategory(formData).then(() => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.getList();\r\n      }).catch(error => {\r\n        console.error('保存表单配置失败:', error);\r\n        this.$modal.msgError(\"保存表单配置失败\");\r\n      });\r\n    },\r\n\r\n    /** 取消表单配置 */\r\n    cancelFormConfig() {\r\n      this.formConfigOpen = false;\r\n      this.formFieldsList = [];\r\n      this.formModulesList = [];\r\n      this.currentCategoryId = null;\r\n      this.selectedTemplate = '';\r\n      this.previewData = {};\r\n      this.previewDialogData = {};\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  display: flex;\r\n  height: 700px;\r\n  gap: 20px;\r\n}\r\n\r\n.config-area {\r\n  flex: 1;\r\n  border-right: 1px solid #e6e6e6;\r\n  padding-right: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-area {\r\n  flex: 1;\r\n  padding-left: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.config-header, .preview-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.config-header h4, .preview-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.template-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.modules-list {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.module-item {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.module-card {\r\n  border: 2px solid #e6e6e6;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.module-card:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.module-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.module-title-section {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.module-icon {\r\n  color: #409EFF;\r\n  font-size: 18px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.module-name-input {\r\n  max-width: 200px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.module-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n\r\n\r\n.fields-list {\r\n  margin-top: 10px;\r\n}\r\n\r\n.field-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.field-card {\r\n  border: 1px solid #e6e6e6;\r\n  margin-left: 20px;\r\n}\r\n\r\n.field-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.field-title {\r\n  font-weight: bold;\r\n  color: #606266;\r\n}\r\n\r\n.field-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.empty-module {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-modules {\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n.preview-content {\r\n  background-color: #f9f9f9;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  min-height: 500px;\r\n}\r\n\r\n.preview-module {\r\n  margin-bottom: 30px;\r\n  background-color: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.preview-module-header {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409EFF;\r\n}\r\n\r\n.preview-module-header h5 {\r\n  margin: 0 0 5px 0;\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n}\r\n\r\n\r\n\r\n.preview-empty-module {\r\n  text-align: center;\r\n  color: #c0c4cc;\r\n  padding: 20px;\r\n  font-style: italic;\r\n}\r\n\r\n.preview-empty {\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n/* 隐藏字段样式 */\r\n.hidden-field {\r\n  opacity: 0.6;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 8px;\r\n  margin-bottom: 8px;\r\n  border-left: 3px solid #909399;\r\n}\r\n\r\n.hidden-field-indicator {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.field-tip {\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.field-tip i {\r\n  margin-right: 4px;\r\n}\r\n\r\n/* 表单对话框样式优化 */\r\n.el-dialog__body {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.el-form-item {\r\n  margin-bottom: 18px;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n\r\n/* 预览对话框样式 */\r\n.preview-dialog-content {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.preview-dialog-module {\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  border: 1px solid #e6e6e6;\r\n  border-radius: 8px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.preview-dialog-module-header {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #409EFF;\r\n}\r\n\r\n.preview-dialog-module-header h4 {\r\n  margin: 0 0 5px 0;\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n}\r\n\r\n\r\n\r\n.preview-dialog-empty-module {\r\n  text-align: center;\r\n  color: #c0c4cc;\r\n  padding: 20px;\r\n  font-style: italic;\r\n}\r\n\r\n.preview-dialog-empty {\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n.el-card {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.el-form-item {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 静态展示内容样式 */\r\n.static-content {\r\n  padding: 10px 15px;\r\n  background-color: #f5f7fa;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  color: #606266;\r\n  line-height: 1.6;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n/* 模块图标样式 */\r\n.module-icon-section {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.module-icon-display {\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  margin-right: 4px;\r\n  background-color: #fff;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.module-icon-display:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.custom-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  object-fit: cover;\r\n  border-radius: 2px;\r\n}\r\n\r\n.default-icon-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n  color: #909399;\r\n  text-align: center;\r\n}\r\n\r\n.default-icon-placeholder i {\r\n  font-size: 12px;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.default-icon-placeholder span {\r\n  line-height: 1;\r\n}\r\n\r\n.icon-edit-btn {\r\n  padding: 4px !important;\r\n  min-height: auto !important;\r\n}\r\n\r\n/* 图标上传器样式 */\r\n.icon-uploader-content {\r\n  text-align: center;\r\n}\r\n\r\n.upload-section h4 {\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.upload-tips {\r\n  margin-top: 15px;\r\n  text-align: left;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409eff;\r\n}\r\n\r\n.upload-tips p {\r\n  margin: 5px 0;\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 预览区域图标样式 */\r\n.preview-module-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-module-icon {\r\n  font-size: 16px;\r\n  color: #409eff;\r\n}\r\n\r\n.preview-module-icon-img {\r\n  width: 16px;\r\n  height: 16px;\r\n  object-fit: cover;\r\n}\r\n\r\n.preview-dialog-module-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-dialog-module-icon {\r\n  font-size: 18px;\r\n  color: #409eff;\r\n}\r\n\r\n.preview-dialog-module-icon-img {\r\n  width: 18px;\r\n  height: 18px;\r\n  object-fit: cover;\r\n}\r\n\r\n/* 类型图标样式 */\r\n.category-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  object-fit: cover;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.no-icon {\r\n  color: #c0c4cc;\r\n  font-size: 12px;\r\n}\r\n\r\n.form-tip {\r\n  margin-top: 8px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.4;\r\n}\r\n\r\n.form-tip p {\r\n  margin: 2px 0;\r\n}\r\n\r\n.form-tip {\r\n  margin-top: 8px;\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.4;\r\n}\r\n\r\n.form-tip p {\r\n  margin: 2px 0;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6iBA,IAAAA,eAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAH,YAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,YAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,OAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,MAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAG,cAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,cAAA;MAAA;MACAC,eAAA;MAAA;MACAC,gBAAA;MACAC,WAAA;MAAA;MACAC,iBAAA;MAAA;MACA;MACAC,gBAAA;MACAC,kBAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IAEA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,kCAAA,OAAA3B,WAAA,EAAA4B,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA7B,YAAA,GAAAgC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA9B,KAAA,GAAAiC,QAAA,CAAAjC,KAAA;QACA8B,KAAA,CAAAnC,OAAA;MACA;IACA;IAGA;IACAwC,MAAA,WAAAA,OAAA;MACA,KAAAhC,IAAA;MACA,KAAAiC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA3B,IAAA;QACA4B,UAAA;QACA9B,YAAA;QACAO,YAAA;QACAwB,iBAAA;QACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAjC,MAAA;QACAkC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAxC,WAAA,CAAAC,OAAA;MACA,KAAAuB,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnD,GAAA,GAAAmD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAZ,UAAA;MAAA;MACA,KAAAxC,MAAA,GAAAkD,SAAA,CAAAG,MAAA;MACA,KAAApD,QAAA,IAAAiD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAf,KAAA;MACA,KAAAjC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAkD,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAlB,KAAA;MACA,IAAAC,UAAA,GAAAgB,GAAA,CAAAhB,UAAA,SAAAzC,GAAA;MACA,IAAA2D,iCAAA,EAAAlB,UAAA,EAAAL,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAA7C,IAAA,GAAAwB,QAAA,CAAAvC,IAAA;QACA4D,MAAA,CAAAnD,IAAA;QACAmD,MAAA,CAAApD,KAAA;MACA;IACA;IACA,WACAsD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhD,IAAA,CAAA4B,UAAA;YACA,IAAAwB,oCAAA,EAAAJ,MAAA,CAAAhD,IAAA,EAAAuB,IAAA,WAAAC,QAAA;cACAwB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtD,IAAA;cACAsD,MAAA,CAAA7B,OAAA;YACA;UACA;YACA,IAAAoC,iCAAA,EAAAP,MAAA,CAAAhD,IAAA,EAAAuB,IAAA,WAAAC,QAAA;cACAwB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtD,IAAA;cACAsD,MAAA,CAAA7B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,WAAA,GAAAd,GAAA,CAAAhB,UAAA,SAAAzC,GAAA;MACA,KAAAkE,MAAA,CAAAM,OAAA,oBAAAD,WAAA,aAAAnC,IAAA;QACA,WAAAqC,iCAAA,EAAAF,WAAA;MACA,GAAAnC,IAAA;QACAkC,MAAA,CAAAtC,OAAA;QACAsC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,sCAAAC,cAAA,CAAAC,OAAA,MACA,KAAAtE,WAAA,2CAAAuE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,WACAC,gBAAA,WAAAA,iBAAAzB,GAAA;MAAA,IAAA0B,MAAA;MACA,IAAAlB,oCAAA,EAAAR,GAAA,EAAArB,IAAA,WAAAC,QAAA;QACA8C,MAAA,CAAAjB,MAAA,CAAAC,UAAA;QACAgB,MAAA,CAAAnD,OAAA;MACA;IACA;IAEA,eACAoD,gBAAA,WAAAA,iBAAA3B,GAAA;MAAA,IAAA4B,MAAA;MACA,KAAA/D,iBAAA,GAAAmC,GAAA,CAAAhB,UAAA;MACA,KAAAlB,cAAA;MACA,KAAAC,eAAA;;MAEA;MACA,IAAAiC,GAAA,CAAA6B,UAAA;QACA;UACA,IAAAC,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAhC,GAAA,CAAA6B,UAAA;;UAEA;UACA,IAAAI,KAAA,CAAAC,OAAA,CAAAJ,UAAA,KAAAA,UAAA,CAAAjC,MAAA,QAAAiC,UAAA,IAAAK,cAAA;YACA;YACA,KAAApE,eAAA,GAAA+D,UAAA;YACA;YACA,KAAA/D,eAAA,CAAAqE,OAAA,WAAAC,MAAA;cACA,IAAAA,MAAA,CAAAC,MAAA;gBACAD,MAAA,CAAAC,MAAA,CAAAF,OAAA,WAAAG,KAAA;kBACA,IAAAA,KAAA,CAAAC,MAAA,KAAAC,SAAA;oBACAb,MAAA,CAAAc,IAAA,CAAAH,KAAA;kBACA;gBACA;cACA;YACA;UACA,WAAAN,KAAA,CAAAC,OAAA,CAAAJ,UAAA;YACA;YACA,KAAAhE,cAAA,GAAAgE,UAAA;YACA,IAAAA,UAAA,CAAAjC,MAAA;cACA;cACAiC,UAAA,CAAAM,OAAA,WAAAG,KAAA;gBACA,IAAAA,KAAA,CAAAC,MAAA,KAAAC,SAAA;kBACAb,MAAA,CAAAc,IAAA,CAAAH,KAAA;gBACA;cACA;cACA,KAAAxE,eAAA;gBACA5B,IAAA;gBACAwG,WAAA;gBACAL,MAAA,EAAAR;cACA;YACA;UACA;QACA,SAAAc,CAAA;UACAC,OAAA,CAAAC,KAAA,cAAAF,CAAA;UACA,KAAA9E,cAAA;UACA,KAAAC,eAAA;QACA;MACA;MAEA,KAAAJ,cAAA;MACA,KAAAoF,eAAA;IACA;IAEA,cACAA,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA/E,WAAA;MACA,KAAAF,eAAA,CAAAqE,OAAA,WAAAC,MAAA;QACA,IAAAA,MAAA,CAAAC,MAAA;UACAD,MAAA,CAAAC,MAAA,CAAAF,OAAA,WAAAG,KAAA;YACA,IAAAA,KAAA,CAAApG,IAAA;cACA,IAAAoG,KAAA,CAAAU,IAAA;gBACAD,MAAA,CAAA/E,WAAA,CAAAsE,KAAA,CAAApG,IAAA;cACA,WAAAoG,KAAA,CAAAU,IAAA;gBACAD,MAAA,CAAA/E,WAAA,CAAAsE,KAAA,CAAApG,IAAA;cACA,WAAAoG,KAAA,CAAAU,IAAA,eAAAV,KAAA,CAAAU,IAAA;gBACAD,MAAA,CAAA/E,WAAA,CAAAsE,KAAA,CAAApG,IAAA;cACA;gBACA6G,MAAA,CAAA/E,WAAA,CAAAsE,KAAA,CAAApG,IAAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA,aACA+G,aAAA,WAAAA,cAAA;MACA,IAAAC,SAAA;QACAhH,IAAA;QACAwG,WAAA;QACAL,MAAA;QACAc,IAAA;MACA;MACA,KAAArF,eAAA,CAAAsF,IAAA,CAAAF,SAAA;IACA;IAEA,WACAG,YAAA,WAAAA,aAAAC,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAV,IAAA;MACA,GAAAtE,IAAA;QACA6E,MAAA,CAAAzF,eAAA,CAAA6F,MAAA,CAAAL,WAAA;QACAC,MAAA,CAAAK,QAAA,CAAAC,OAAA;MACA,GAAA7C,KAAA;IACA;IAEA,WACA8C,YAAA,WAAAA,aAAAR,WAAA;MACA,IAAAA,WAAA;QACA,IAAAS,IAAA,QAAAjG,eAAA,CAAAwF,WAAA;QACA,KAAAb,IAAA,MAAA3E,eAAA,EAAAwF,WAAA,OAAAxF,eAAA,CAAAwF,WAAA;QACA,KAAAb,IAAA,MAAA3E,eAAA,EAAAwF,WAAA,MAAAS,IAAA;MACA;IACA;IAEA,WACAC,cAAA,WAAAA,eAAAV,WAAA;MACA,IAAAA,WAAA,QAAAxF,eAAA,CAAA8B,MAAA;QACA,IAAAmE,IAAA,QAAAjG,eAAA,CAAAwF,WAAA;QACA,KAAAb,IAAA,MAAA3E,eAAA,EAAAwF,WAAA,OAAAxF,eAAA,CAAAwF,WAAA;QACA,KAAAb,IAAA,MAAA3E,eAAA,EAAAwF,WAAA,MAAAS,IAAA;MACA;IACA;IAEA,aACAE,kBAAA,WAAAA,mBAAA7B,MAAA;MACA,KAAAA,MAAA,CAAAlG,IAAA,IAAAkG,MAAA,CAAAlG,IAAA,CAAAgI,IAAA;QACA9B,MAAA,CAAAlG,IAAA;MACA;IACA;IAEA,cACAiI,gBAAA,WAAAA,iBAAAb,WAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,QAAA;QACAC,KAAA;QACApI,IAAA;QACA8G,IAAA;QACA3F,QAAA;QACAkF,MAAA;QAAA;QACAgC,OAAA;QACAC,WAAA;QACAC,aAAA;MACA;MACA,UAAA3G,eAAA,CAAAwF,WAAA,EAAAjB,MAAA;QACA,KAAAI,IAAA,MAAA3E,eAAA,CAAAwF,WAAA;MACA;MACA,KAAAxF,eAAA,CAAAwF,WAAA,EAAAjB,MAAA,CAAAe,IAAA,CAAAiB,QAAA;MACA;MACA,KAAAK,SAAA;QACAN,MAAA,CAAAtB,eAAA;MACA;IACA;IAEA,aACA6B,iBAAA,WAAAA,kBAAAC,OAAA,EAAAtB,WAAA;MACA,KAAAnF,kBAAA,GAAAmF,WAAA;MAEA,IAAAsB,OAAA;QACA,KAAAxG,YAAA;QACA,KAAAF,gBAAA;MACA,WAAA0G,OAAA;QACA,KAAAnC,IAAA,MAAA3E,eAAA,CAAAwF,WAAA;MACA;IACA;IAEA,cACAuB,gBAAA,WAAAA,iBAAAvB,WAAA;MACA,KAAAnF,kBAAA,GAAAmF,WAAA;MACA,KAAAlF,YAAA;MACA,KAAAF,gBAAA;IACA;IAEA,aACA4G,iBAAA,WAAAA,kBAAA;MACA,SAAA3G,kBAAA,cAAAC,YAAA;QACA,KAAAqE,IAAA,MAAA3E,eAAA,MAAAK,kBAAA,gBAAAC,YAAA;QACA,KAAAwF,QAAA,CAAAC,OAAA;MACA;MACA,KAAA3F,gBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA,aACA2G,gBAAA,WAAAA,iBAAA;MACA,KAAA7G,gBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA,iBACA4G,iBAAA,WAAAA,kBAAA1C,KAAA;MACA;MACA,IAAA2C,cAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,KAAA3C,KAAA,CAAAkC,WAAA,IAAAlC,KAAA,CAAAkC,WAAA;QACAlC,KAAA,CAAAkC,WAAA,GAAAS,cAAA,CAAA3C,KAAA,CAAAU,IAAA;MACA;;MAEA;MACA,IAAAV,KAAA,CAAAU,IAAA;QACAV,KAAA,CAAAjF,QAAA;QACA,KAAAiF,KAAA,CAAAmC,aAAA;UACAnC,KAAA,CAAAmC,aAAA;QACA;MACA;IACA;IAEA,mBACAS,mBAAA,WAAAA,oBAAA;MACA;IAAA,CACA;IAEA,cACAC,qBAAA,WAAAA,sBAAA7B,WAAA,EAAA8B,UAAA;MACA,KAAAtH,eAAA,CAAAwF,WAAA,EAAAjB,MAAA,CAAAsB,MAAA,CAAAyB,UAAA;IACA;IAEA,cACAC,mBAAA,WAAAA,oBAAA/B,WAAA,EAAA8B,UAAA;MACA,IAAA/C,MAAA,QAAAvE,eAAA,CAAAwF,WAAA,EAAAjB,MAAA;MACA,IAAA+C,UAAA;QACA,IAAArB,IAAA,GAAA1B,MAAA,CAAA+C,UAAA;QACA,KAAA3C,IAAA,CAAAJ,MAAA,EAAA+C,UAAA,EAAA/C,MAAA,CAAA+C,UAAA;QACA,KAAA3C,IAAA,CAAAJ,MAAA,EAAA+C,UAAA,MAAArB,IAAA;MACA;IACA;IAEA,cACAuB,qBAAA,WAAAA,sBAAAhC,WAAA,EAAA8B,UAAA;MACA,IAAA/C,MAAA,QAAAvE,eAAA,CAAAwF,WAAA,EAAAjB,MAAA;MACA,IAAA+C,UAAA,GAAA/C,MAAA,CAAAzC,MAAA;QACA,IAAAmE,IAAA,GAAA1B,MAAA,CAAA+C,UAAA;QACA,KAAA3C,IAAA,CAAAJ,MAAA,EAAA+C,UAAA,EAAA/C,MAAA,CAAA+C,UAAA;QACA,KAAA3C,IAAA,CAAAJ,MAAA,EAAA+C,UAAA,MAAArB,IAAA;MACA;IACA;IAEA,oBACAwB,YAAA,WAAAA,aAAA;MACA;MACA,SAAAzH,eAAA,CAAA8B,MAAA;QACA,KAAAqD,aAAA;QACA,KAAAnF,eAAA,IAAA5B,IAAA;MACA;;MAEA;MACA,KAAAiI,gBAAA;IACA;IAIA,aACAqB,iBAAA,WAAAA,kBAAAlD,KAAA;MACA,IAAAA,KAAA,CAAAgC,KAAA;QACA;QACA,IAAAmB,OAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;;QAEA;QACA,IAAAA,OAAA,CAAAnD,KAAA,CAAAgC,KAAA;UACAhC,KAAA,CAAApG,IAAA,GAAAuJ,OAAA,CAAAnD,KAAA,CAAAgC,KAAA;QACA;UACA;UACA,IAAApI,IAAA,GAAAoG,KAAA,CAAAgC,KAAA,CACAoB,OAAA;UAAA,CACAA,OAAA;UAAA,CACAC,WAAA;;UAEA;UACA,sBAAAC,IAAA,CAAA1J,IAAA;YACA;YACAA,IAAA,GAAAA,IAAA,CACAwJ,OAAA,gBACAA,OAAA,gBACAA,OAAA,gBACAA,OAAA,gBACAA,OAAA,mBACAA,OAAA,iBACAA,OAAA,iBACAA,OAAA,mBACAA,OAAA,uBACAA,OAAA,gBACAA,OAAA,kBACAA,OAAA,uBACAA,OAAA,iBACAA,OAAA,kBACAA,OAAA,oBACAA,OAAA,iBACAA,OAAA;;YAEA;YACA,sBAAAE,IAAA,CAAA1J,IAAA;cACAA,IAAA,cAAAoF,IAAA,CAAAuE,GAAA,GAAAC,QAAA,GAAAC,KAAA;YACA;UACA;UAEAzD,KAAA,CAAApG,IAAA,GAAAA,IAAA;QACA;MACA;IACA;IAEA,aACA8J,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAAlI,gBAAA;QACA,KAAAmI,kBAAA;MACA,gBAAAnI,gBAAA;QACA,KAAAoI,iBAAA;MACA,gBAAApI,gBAAA;QACA,KAAAqI,qBAAA;MACA;MACA;MACA,KAAA1B,SAAA;QACAuB,MAAA,CAAAnD,eAAA;MACA;IACA;IAEA,eACAoD,kBAAA,WAAAA,mBAAA;MAAA,IAAAG,MAAA;MACA,KAAAvI,eAAA,IACA;QACA5B,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,GACA;QACAvI,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,GACA;QACAvI,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,EACA;;MAEA;MACA,KAAA3G,eAAA,CAAAqE,OAAA,WAAAC,MAAA;QACAA,MAAA,CAAAC,MAAA,CAAAF,OAAA,WAAAG,KAAA;UACA+D,MAAA,CAAAb,iBAAA,CAAAlD,KAAA;QACA;MACA;IACA;IAEA,eACA6D,iBAAA,WAAAA,kBAAA;MAAA,IAAAG,OAAA;MACA,KAAAxI,eAAA,IACA;QACA5B,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,GACA;QACAvI,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,GACA;QACAvI,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,EACA;;MAEA;MACA,KAAA3G,eAAA,CAAAqE,OAAA,WAAAC,MAAA;QACAA,MAAA,CAAAC,MAAA,CAAAF,OAAA,WAAAG,KAAA;UACAgE,OAAA,CAAAd,iBAAA,CAAAlD,KAAA;QACA;MACA;IACA;IAEA,eACA8D,qBAAA,WAAAA,sBAAA;MAAA,IAAAG,OAAA;MACA,KAAAzI,eAAA,IACA;QACA5B,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,GACA;QACAvI,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,GACA;QACAvI,IAAA;QACAwG,WAAA;QACAL,MAAA,GACA;UACAiC,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA,GACA;UACAH,KAAA;UACApI,IAAA;UACA8G,IAAA;UACA3F,QAAA;UACAkF,MAAA;UACAgC,OAAA;UACAC,WAAA;UACAC,aAAA;QACA;MAEA,EACA;;MAEA;MACA,KAAA3G,eAAA,CAAAqE,OAAA,WAAAC,MAAA;QACAA,MAAA,CAAAC,MAAA,CAAAF,OAAA,WAAAG,KAAA;UACAiE,OAAA,CAAAf,iBAAA,CAAAlD,KAAA;QACA;MACA;IACA;IAEA,aACAkE,eAAA,WAAAA,gBAAAlE,KAAA;MACA,KAAAA,KAAA,CAAAiC,OAAA,IAAAjC,KAAA,CAAAiC,OAAA,CAAAL,IAAA;MACA,OAAA5B,KAAA,CAAAiC,OAAA,CAAAkC,KAAA,MAAA/G,GAAA,WAAAgH,MAAA;QAAA,OAAAA,MAAA,CAAAxC,IAAA;MAAA,GAAAyC,MAAA,WAAAD,MAAA;QAAA,OAAAA,MAAA;MAAA;IACA;IAEA,WACAE,WAAA,WAAAA,YAAA;MACA,KAAAC,qBAAA;MACA,KAAAlJ,WAAA;IACA;IAEA,iBACAkJ,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MACA,KAAA7I,iBAAA;MACA,KAAAH,eAAA,CAAAqE,OAAA,WAAAC,MAAA;QACA,IAAAA,MAAA,CAAAC,MAAA;UACAD,MAAA,CAAAC,MAAA,CAAAF,OAAA,WAAAG,KAAA;YACA,IAAAA,KAAA,CAAApG,IAAA;cACA,IAAAoG,KAAA,CAAAU,IAAA;gBACA8D,OAAA,CAAA7I,iBAAA,CAAAqE,KAAA,CAAApG,IAAA;cACA,WAAAoG,KAAA,CAAAU,IAAA;gBACA8D,OAAA,CAAA7I,iBAAA,CAAAqE,KAAA,CAAApG,IAAA;cACA,WAAAoG,KAAA,CAAAU,IAAA,eAAAV,KAAA,CAAAU,IAAA;gBACA8D,OAAA,CAAA7I,iBAAA,CAAAqE,KAAA,CAAApG,IAAA;cACA;gBACA4K,OAAA,CAAA7I,iBAAA,CAAAqE,KAAA,CAAApG,IAAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA,aACA6K,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,UAAApJ,iBAAA;QACA,KAAA4C,MAAA,CAAAyG,QAAA;QACA;MACA;;MAEA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAApJ,eAAA,CAAA8B,MAAA,EAAAsH,CAAA;QACA,IAAA9E,MAAA,QAAAtE,eAAA,CAAAoJ,CAAA;QACA,KAAA9E,MAAA,CAAAlG,IAAA,IAAAkG,MAAA,CAAAlG,IAAA,CAAAgI,IAAA;UACA,KAAA1D,MAAA,CAAAyG,QAAA,UAAA5F,MAAA,CAAA6F,CAAA;UACA;QACA;QAEA,SAAAC,CAAA,MAAAA,CAAA,GAAA/E,MAAA,CAAAC,MAAA,CAAAzC,MAAA,EAAAuH,CAAA;UACA,IAAA7E,KAAA,GAAAF,MAAA,CAAAC,MAAA,CAAA8E,CAAA;UACA,KAAA7E,KAAA,CAAAgC,KAAA,IAAAhC,KAAA,CAAAgC,KAAA,CAAAJ,IAAA;YACA,KAAA1D,MAAA,CAAAyG,QAAA,kBAAA5F,MAAA,CAAAe,MAAA,CAAAlG,IAAA,oBAAAmF,MAAA,CAAA8F,CAAA;YACA;UACA;QACA;MACA;MAEA,IAAAC,QAAA;QACArI,UAAA,OAAAnB,iBAAA;QACAgE,UAAA,EAAAE,IAAA,CAAAuF,SAAA,MAAAvJ,eAAA;MACA;MAEA,IAAAyC,oCAAA,EAAA6G,QAAA,EAAA1I,IAAA;QACAsI,OAAA,CAAAxG,MAAA,CAAAC,UAAA;QACAuG,OAAA,CAAAtJ,cAAA;QACAsJ,OAAA,CAAA1I,OAAA;MACA,GAAA0C,KAAA,WAAA6B,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACAmE,OAAA,CAAAxG,MAAA,CAAAyG,QAAA;MACA;IACA;IAEA,aACAK,gBAAA,WAAAA,iBAAA;MACA,KAAA5J,cAAA;MACA,KAAAG,cAAA;MACA,KAAAC,eAAA;MACA,KAAAF,iBAAA;MACA,KAAAG,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,iBAAA;IACA;EACA;AACA", "ignoreList": []}]}