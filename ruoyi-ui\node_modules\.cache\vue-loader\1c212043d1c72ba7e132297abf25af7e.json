{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=template&id=67d8a4ab", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754295969458}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1mb3JtIDptb2RlbD0icXVlcnlQYXJhbXMiIHJlZj0icXVlcnlGb3JtIiBzaXplPSJzbWFsbCIgOmlubGluZT0idHJ1ZSIgdi1zaG93PSJzaG93U2VhcmNoIiBsYWJlbC13aWR0aD0iNjhweCI+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmqKHlnZflkI3np7AiIHByb3A9Im1vZHVsZU5hbWUiPgogICAgICA8ZWwtaW5wdXQKICAgICAgICB2LW1vZGVsPSJxdWVyeVBhcmFtcy5tb2R1bGVOYW1lIgogICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmqKHlnZflkI3np7AiCiAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgQGtleXVwLmVudGVyLm5hdGl2ZT0iaGFuZGxlUXVlcnkiCiAgICAgIC8+CiAgICA8L2VsLWZvcm0taXRlbT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaooeWdl+S7o+eggSIgcHJvcD0ibW9kdWxlQ29kZSI+CiAgICAgIDxlbC1pbnB1dAogICAgICAgIHYtbW9kZWw9InF1ZXJ5UGFyYW1zLm1vZHVsZUNvZGUiCiAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaooeWdl+S7o+eggSIKICAgICAgICBjbGVhcmFibGUKICAgICAgICBAa2V5dXAuZW50ZXIubmF0aXZlPSJoYW5kbGVRdWVyeSIKICAgICAgLz4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6ZO+5o6l57G75Z6LIiBwcm9wPSJsaW5rVHlwZSI+CiAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0icXVlcnlQYXJhbXMubGlua1R5cGUiIHBsYWNlaG9sZGVyPSLor7fpgInmi6npk77mjqXnsbvlnosiIGNsZWFyYWJsZT4KICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlhoXpg6jpobXpnaIiIHZhbHVlPSIxIiAvPgogICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuWklumDqOmTvuaOpSIgdmFsdWU9IjIiIC8+CiAgICAgIDwvZWwtc2VsZWN0PgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnirbmgIEiIHByb3A9InN0YXR1cyI+CiAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0icXVlcnlQYXJhbXMuc3RhdHVzIiBwbGFjZWhvbGRlcj0i6K+36YCJ5oup54q25oCBIiBjbGVhcmFibGU+CiAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5q2j5bi4IiB2YWx1ZT0iMCIgLz4KICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlgZznlKgiIHZhbHVlPSIxIiAvPgogICAgICA8L2VsLXNlbGVjdD4KICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPGVsLWZvcm0taXRlbT4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLXNlYXJjaCIgc2l6ZT0ibWluaSIgQGNsaWNrPSJoYW5kbGVRdWVyeSI+5pCc57SiPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gaWNvbj0iZWwtaWNvbi1yZWZyZXNoIiBzaXplPSJtaW5pIiBAY2xpY2s9InJlc2V0UXVlcnkiPumHjee9rjwvZWwtYnV0dG9uPgogICAgPC9lbC1mb3JtLWl0ZW0+CiAgPC9lbC1mb3JtPgoKICA8ZWwtcm93IDpndXR0ZXI9IjEwIiBjbGFzcz0ibWI4Ij4KICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgIHBsYWluCiAgICAgICAgaWNvbj0iZWwtaWNvbi1wbHVzIgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVBZGQiCiAgICAgICAgdi1oYXNQZXJtaT0iWydjb25maWc6aG9tZW1vZHVsZTphZGQnXSIKICAgICAgPuaWsOWinjwvZWwtYnV0dG9uPgogICAgPC9lbC1jb2w+CiAgICA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0ic3VjY2VzcyIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tZWRpdCIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIDpkaXNhYmxlZD0ic2luZ2xlIgogICAgICAgIEBjbGljaz0iaGFuZGxlVXBkYXRlIgogICAgICAgIHYtaGFzUGVybWk9IlsnY29uZmlnOmhvbWVtb2R1bGU6ZWRpdCddIgogICAgICA+5L+u5pS5PC9lbC1idXR0b24+CiAgICA8L2VsLWNvbD4KICAgIDxlbC1jb2wgOnNwYW49IjEuNSI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJkYW5nZXIiCiAgICAgICAgcGxhaW4KICAgICAgICBpY29uPSJlbC1pY29uLWRlbGV0ZSIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIDpkaXNhYmxlZD0ibXVsdGlwbGUiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVEZWxldGUiCiAgICAgICAgdi1oYXNQZXJtaT0iWydjb25maWc6aG9tZW1vZHVsZTpyZW1vdmUnXSIKICAgICAgPuWIoOmZpDwvZWwtYnV0dG9uPgogICAgPC9lbC1jb2w+CiAgICA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0id2FybmluZyIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tZG93bmxvYWQiCiAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICBAY2xpY2s9ImhhbmRsZUV4cG9ydCIKICAgICAgICB2LWhhc1Blcm1pPSJbJ2NvbmZpZzpob21lbW9kdWxlOmV4cG9ydCddIgogICAgICA+5a+85Ye6PC9lbC1idXR0b24+CiAgICA8L2VsLWNvbD4KICAgIDxyaWdodC10b29sYmFyIDpzaG93U2VhcmNoLnN5bmM9InNob3dTZWFyY2giIEBxdWVyeVRhYmxlPSJnZXRMaXN0Ij48L3JpZ2h0LXRvb2xiYXI+CiAgPC9lbC1yb3c+CgogIDxlbC10YWJsZSB2LWxvYWRpbmc9ImxvYWRpbmciIDpkYXRhPSJob21lTW9kdWxlTGlzdCIgQHNlbGVjdGlvbi1jaGFuZ2U9ImhhbmRsZVNlbGVjdGlvbkNoYW5nZSIgc3R5bGU9IndpZHRoOiAxMDAlIj4KICAgIDxlbC10YWJsZS1jb2x1bW4gdHlwZT0ic2VsZWN0aW9uIiB3aWR0aD0iNTUiIGFsaWduPSJjZW50ZXIiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSJJRCIgYWxpZ249ImNlbnRlciIgcHJvcD0iaWQiIHdpZHRoPSI3MCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaooeWdl+WbvuaghyIgYWxpZ249ImNlbnRlciIgcHJvcD0ibW9kdWxlSWNvbiIgd2lkdGg9IjkwIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8aW1hZ2UtcHJldmlldyB2LWlmPSJzY29wZS5yb3cubW9kdWxlSWNvbiIgOnNyYz0ic2NvcGUucm93Lm1vZHVsZUljb24iIDp3aWR0aD0iNTAiIDpoZWlnaHQ9IjUwIi8+CiAgICAgICAgPHNwYW4gdi1lbHNlPi08L3NwYW4+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaooeWdl+WQjeensCIgYWxpZ249ImNlbnRlciIgcHJvcD0ibW9kdWxlTmFtZSIgd2lkdGg9IjEyMCIgc2hvdy1vdmVyZmxvdy10b29sdGlwIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmqKHlnZfku6PnoIEiIGFsaWduPSJjZW50ZXIiIHByb3A9Im1vZHVsZUNvZGUiIHdpZHRoPSIxNTAiIHNob3ctb3ZlcmZsb3ctdG9vbHRpcCAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i6ZO+5o6l57G75Z6LIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJsaW5rVHlwZSIgd2lkdGg9IjEwMCI+CiAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgPGVsLXRhZyB2LWlmPSJzY29wZS5yb3cubGlua1R5cGUgPT09ICcxJyIgdHlwZT0icHJpbWFyeSIgc2l6ZT0ibWluaSI+5YaF6YOo6aG16Z2iPC9lbC10YWc+CiAgICAgICAgPGVsLXRhZyB2LWVsc2UtaWY9InNjb3BlLnJvdy5saW5rVHlwZSA9PT0gJzInIiB0eXBlPSJ3YXJuaW5nIiBzaXplPSJtaW5pIj7lpJbpg6jpk77mjqU8L2VsLXRhZz4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i6Lez6L2s5Zyw5Z2AIiBhbGlnbj0iY2VudGVyIiBzaG93LW92ZXJmbG93LXRvb2x0aXAgbWluLXdpZHRoPSIyMDAiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgIDxzcGFuIHYtaWY9InNjb3BlLnJvdy5saW5rVHlwZSA9PT0gJzEnIj57eyBzY29wZS5yb3cubW9kdWxlVXJsIHx8ICctJyB9fTwvc3Bhbj4KICAgICAgICA8c3BhbiB2LWVsc2UtaWY9InNjb3BlLnJvdy5saW5rVHlwZSA9PT0gJzInIj57eyBzY29wZS5yb3cuZXh0ZXJuYWxVcmwgfHwgJy0nIH19PC9zcGFuPgogICAgICAgIDxzcGFuIHYtZWxzZT4tPC9zcGFuPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmjpLluo8iIGFsaWduPSJjZW50ZXIiIHByb3A9InNvcnRPcmRlciIgd2lkdGg9IjExMCI+CiAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgdi1tb2RlbD0ic2NvcGUucm93LnNvcnRPcmRlciIKICAgICAgICAgIDptaW49IjAiCiAgICAgICAgICA6bWF4PSI5OTk5IgogICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgIGNvbnRyb2xzLXBvc2l0aW9uPSJyaWdodCIKICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZVNvcnRPcmRlckNoYW5nZShzY29wZS5yb3cpIgogICAgICAgICAgc3R5bGU9IndpZHRoOiA5MHB4OyIKICAgICAgICAvPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLnirbmgIEiIGFsaWduPSJjZW50ZXIiIHByb3A9InN0YXR1cyIgd2lkdGg9IjkwIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8ZWwtdGFnIHYtaWY9InNjb3BlLnJvdy5zdGF0dXMgPT09ICcwJyIgdHlwZT0ic3VjY2VzcyIgc2l6ZT0ibWluaSI+5q2j5bi4PC9lbC10YWc+CiAgICAgICAgPGVsLXRhZyB2LWVsc2UgdHlwZT0iZGFuZ2VyIiBzaXplPSJtaW5pIj7lgZznlKg8L2VsLXRhZz4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5pON5L2cIiBhbGlnbj0iY2VudGVyIiB3aWR0aD0iMTMwIiBjbGFzcy1uYW1lPSJzbWFsbC1wYWRkaW5nIGZpeGVkLXdpZHRoIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgIGljb249ImVsLWljb24tZWRpdCIKICAgICAgICAgIEBjbGljaz0iaGFuZGxlVXBkYXRlKHNjb3BlLnJvdykiCiAgICAgICAgICB2LWhhc1Blcm1pPSJbJ2NvbmZpZzpob21lbW9kdWxlOmVkaXQnXSIKICAgICAgICA+5L+u5pS5PC9lbC1idXR0b24+CiAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICBpY29uPSJlbC1pY29uLWRlbGV0ZSIKICAgICAgICAgIEBjbGljaz0iaGFuZGxlRGVsZXRlKHNjb3BlLnJvdykiCiAgICAgICAgICB2LWhhc1Blcm1pPSJbJ2NvbmZpZzpob21lbW9kdWxlOnJlbW92ZSddIgogICAgICAgID7liKDpmaQ8L2VsLWJ1dHRvbj4KICAgICAgPC90ZW1wbGF0ZT4KICAgIDwvZWwtdGFibGUtY29sdW1uPgogIDwvZWwtdGFibGU+CiAgCiAgPHBhZ2luYXRpb24KICAgIHYtc2hvdz0idG90YWw+MCIKICAgIDp0b3RhbD0idG90YWwiCiAgICA6cGFnZS5zeW5jPSJxdWVyeVBhcmFtcy5wYWdlTnVtIgogICAgOmxpbWl0LnN5bmM9InF1ZXJ5UGFyYW1zLnBhZ2VTaXplIgogICAgQHBhZ2luYXRpb249ImdldExpc3QiCiAgLz4KCiAgPCEtLSDmt7vliqDmiJbkv67mlLnlsI/nqIvluo/pppbpobXlip/og73mqKHlnZflr7nor53moYYgLS0+CiAgPGVsLWRpYWxvZyA6dGl0bGU9InRpdGxlIiA6dmlzaWJsZS5zeW5jPSJvcGVuIiB3aWR0aD0iNjAwcHgiIGFwcGVuZC10by1ib2R5PgogICAgPGVsLWZvcm0gcmVmPSJmb3JtIiA6bW9kZWw9ImZvcm0iIDpydWxlcz0icnVsZXMiIGxhYmVsLXdpZHRoPSIxMDBweCI+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaooeWdl+WQjeensCIgcHJvcD0ibW9kdWxlTmFtZSI+CiAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0ubW9kdWxlTmFtZSIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaooeWdl+WQjeensCIgbWF4bGVuZ3RoPSIxMDAiIC8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmqKHlnZfku6PnoIEiIHByb3A9Im1vZHVsZUNvZGUiPgogICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgdi1tb2RlbD0iZm9ybS5tb2R1bGVDb2RlIgogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaooeWdl+S7o+eggSIKICAgICAgICAgIG1heGxlbmd0aD0iNTAiCiAgICAgICAgICA6ZGlzYWJsZWQ9ImZvcm0uaWQgIT0gbnVsbCIKICAgICAgICAvPgogICAgICAgIDxkaXYgc3R5bGU9ImNvbG9yOiAjOTA5Mzk5OyBmb250LXNpemU6IDEycHg7IG1hcmdpbi10b3A6IDVweDsiPgogICAgICAgICAg5qih5Z2X5Luj56CB55So5LqO57O757uf5YaF6YOo6K+G5Yir77yM5b+F6aG75ZSv5LiA77yM5Yib5bu65ZCO5LiN5Y+v5L+u5pS5CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmqKHlnZflm77moIciIHByb3A9Im1vZHVsZUljb24iPgogICAgICAgIDxpbWFnZS11cGxvYWQgdi1tb2RlbD0iZm9ybS5tb2R1bGVJY29uIi8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpk77mjqXnsbvlnosiIHByb3A9ImxpbmtUeXBlIj4KICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAgdi1tb2RlbD0iZm9ybS5saW5rVHlwZSIgQGNoYW5nZT0iaGFuZGxlTGlua1R5cGVDaGFuZ2UiPgogICAgICAgICAgPGVsLXJhZGlvIGxhYmVsPSIxIj7lhoXpg6jpobXpnaI8L2VsLXJhZGlvPgogICAgICAgICAgPGVsLXJhZGlvIGxhYmVsPSIyIj7lpJbpg6jpk77mjqU8L2VsLXJhZGlvPgogICAgICAgIDwvZWwtcmFkaW8tZ3JvdXA+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIHYtaWY9ImZvcm0ubGlua1R5cGUgPT09ICcxJyIgbGFiZWw9IuWGhemDqOmTvuaOpSIgcHJvcD0ibW9kdWxlVXJsIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5tb2R1bGVVcmwiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlsI/nqIvluo/pobXpnaLot6/lvoTvvIzlpoLvvJovcGFnZXMvaG9tZS9pbmRleCIgLz4KICAgICAgICA8ZGl2IHN0eWxlPSJjb2xvcjogIzkwOTM5OTsgZm9udC1zaXplOiAxMnB4OyBtYXJnaW4tdG9wOiA1cHg7Ij4KICAgICAgICAgIOWwj+eoi+W6j+WGhemDqOmhtemdoui3r+W+hO+8jOS7pSAvIOW8gOWktAogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSB2LWlmPSJmb3JtLmxpbmtUeXBlID09PSAnMiciIGxhYmVsPSLlpJbpg6jpk77mjqUiIHByb3A9ImV4dGVybmFsVXJsIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5leHRlcm5hbFVybCIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWujOaVtOeahFVSTOWcsOWdgO+8jOWmgu+8mmh0dHBzOi8vd3d3LmV4YW1wbGUuY29tIiAvPgogICAgICAgIDxkaXYgc3R5bGU9ImNvbG9yOiAjOTA5Mzk5OyBmb250LXNpemU6IDEycHg7IG1hcmdpbi10b3A6IDVweDsiPgogICAgICAgICAg5aSW6YOo572R56uZ6ZO+5o6l77yM6ZyA6KaB5YyF5ZCrIGh0dHA6Ly8g5oiWIGh0dHBzOi8vCiAgICAgICAgPC9kaXY+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmjpLluo8iIHByb3A9InNvcnRPcmRlciI+CiAgICAgICAgPGVsLWlucHV0LW51bWJlciB2LW1vZGVsPSJmb3JtLnNvcnRPcmRlciIgOm1pbj0iMCIgOm1heD0iOTk5OSIgLz4KICAgICAgICA8ZGl2IHN0eWxlPSJjb2xvcjogIzkwOTM5OTsgZm9udC1zaXplOiAxMnB4OyBtYXJnaW4tdG9wOiA1cHg7Ij4KICAgICAgICAgIOaVsOWtl+i2iuWwj+i2iumdoOWJjeaYvuekugogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i54q25oCBIiBwcm9wPSJzdGF0dXMiPgogICAgICAgIDxlbC1yYWRpby1ncm91cCB2LW1vZGVsPSJmb3JtLnN0YXR1cyI+CiAgICAgICAgICA8ZWwtcmFkaW8gbGFiZWw9IjAiPuato+W4uDwvZWwtcmFkaW8+CiAgICAgICAgICA8ZWwtcmFkaW8gbGFiZWw9IjEiPuWBnOeUqDwvZWwtcmFkaW8+CiAgICAgICAgPC9lbC1yYWRpby1ncm91cD4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWkh+azqCIgcHJvcD0icmVtYXJrIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5yZW1hcmsiIHR5cGU9InRleHRhcmVhIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5aSH5rOoIiAvPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgIDwvZWwtZm9ybT4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJzdWJtaXRGb3JtIj7noa4g5a6aPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjYW5jZWwiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgo8L2Rpdj4K"}, null]}