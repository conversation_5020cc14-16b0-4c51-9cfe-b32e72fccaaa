{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-2d0c033e\"],{\"417b\":function(e,t,a){\"use strict\";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"唯一标识\",prop:\"uniqueCode\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入唯一标识\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.uniqueCode,callback:function(t){e.$set(e.queryParams,\"uniqueCode\",t)},expression:\"queryParams.uniqueCode\"}})],1),a(\"el-form-item\",{attrs:{label:\"标题\",prop:\"title\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入标题\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,\"title\",t)},expression:\"queryParams.title\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-select\",{attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,\"status\",t)},expression:\"queryParams.status\"}},[a(\"el-option\",{attrs:{label:\"正常\",value:\"0\"}}),a(\"el-option\",{attrs:{label:\"停用\",value:\"1\"}})],1)],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"config:roadshow:add\"],expression:\"['config:roadshow:add']\"}],attrs:{type:\"primary\",plain:\"\",icon:\"el-icon-plus\",size:\"mini\"},on:{click:e.handleAdd}},[e._v(\"新增\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"config:roadshow:edit\"],expression:\"['config:roadshow:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:e.single},on:{click:e.handleUpdate}},[e._v(\"修改\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"config:roadshow:remove\"],expression:\"['config:roadshow:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"config:roadshow:export\"],expression:\"['config:roadshow:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.roadshowList},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"ID\",align:\"center\",prop:\"id\",width:\"80\"}}),a(\"el-table-column\",{attrs:{label:\"唯一标识\",align:\"center\",prop:\"uniqueCode\",width:\"120\"}}),a(\"el-table-column\",{attrs:{label:\"封面图\",align:\"center\",prop:\"coverImage\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[t.row.coverImage?a(\"image-preview\",{attrs:{src:t.row.coverImage,width:80,height:50}}):a(\"span\",[e._v(\"-\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"标题\",align:\"center\",prop:\"title\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"描述\",align:\"center\",prop:\"description\",\"show-overflow-tooltip\":\"\"}}),a(\"el-table-column\",{attrs:{label:\"排序\",align:\"center\",prop:\"sortOrder\",width:\"80\"}}),a(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"0\"===t.row.status?a(\"el-tag\",{attrs:{type:\"success\"}},[e._v(\"正常\")]):a(\"el-tag\",{attrs:{type:\"danger\"}},[e._v(\"停用\")])]}}])}),a(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",prop:\"createTime\",width:\"180\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",[e._v(e._s(e.parseTime(t.row.createTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"config:roadshow:edit\"],expression:\"['config:roadshow:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(\"修改\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"config:roadshow:remove\"],expression:\"['config:roadshow:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:e.title,visible:e.open,width:\"600px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.open=t}}},[a(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"唯一标识\",prop:\"uniqueCode\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入唯一标识\",disabled:null!=e.form.id,maxlength:\"50\"},model:{value:e.form.uniqueCode,callback:function(t){e.$set(e.form,\"uniqueCode\",t)},expression:\"form.uniqueCode\"}}),a(\"div\",{staticStyle:{color:\"#909399\",\"font-size\":\"12px\",\"margin-top\":\"5px\"}},[e._v(\" 唯一标识创建后不可修改，用于系统内部识别 \")])],1),a(\"el-form-item\",{attrs:{label:\"标题\",prop:\"title\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入标题\",maxlength:\"200\"},model:{value:e.form.title,callback:function(t){e.$set(e.form,\"title\",t)},expression:\"form.title\"}})],1),a(\"el-form-item\",{attrs:{label:\"封面图片\",prop:\"coverImage\"}},[a(\"image-upload\",{model:{value:e.form.coverImage,callback:function(t){e.$set(e.form,\"coverImage\",t)},expression:\"form.coverImage\"}})],1),a(\"el-form-item\",{attrs:{label:\"描述\",prop:\"description\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入描述\",rows:4},model:{value:e.form.description,callback:function(t){e.$set(e.form,\"description\",t)},expression:\"form.description\"}})],1),a(\"el-form-item\",{attrs:{label:\"排序\",prop:\"sortOrder\"}},[a(\"el-input-number\",{attrs:{min:0,max:9999},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,\"sortOrder\",t)},expression:\"form.sortOrder\"}})],1),a(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[a(\"el-radio-group\",{model:{value:e.form.status,callback:function(t){e.$set(e.form,\"status\",t)},expression:\"form.status\"}},[a(\"el-radio\",{attrs:{label:\"0\"}},[e._v(\"正常\")]),a(\"el-radio\",{attrs:{label:\"1\"}},[e._v(\"停用\")])],1)],1),a(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[a(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入备注\"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),a(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1)],1)},o=[],i=a(\"5530\"),n=(a(\"d9e2\"),a(\"d81d\"),a(\"d3b7\"),a(\"0643\"),a(\"a573\"),a(\"b775\"));function l(e){return Object(n[\"a\"])({url:\"/miniapp/roadshow/list\",method:\"get\",params:e})}function s(e){return Object(n[\"a\"])({url:\"/miniapp/roadshow/\"+e,method:\"get\"})}function u(e){return Object(n[\"a\"])({url:\"/miniapp/roadshow\",method:\"post\",data:e})}function c(e){return Object(n[\"a\"])({url:\"/miniapp/roadshow\",method:\"put\",data:e})}function d(e){return Object(n[\"a\"])({url:\"/miniapp/roadshow/\"+e,method:\"delete\"})}function m(e){return Object(n[\"a\"])({url:\"/miniapp/roadshow/checkUniqueCodeUnique\",method:\"post\",data:e})}var p={name:\"Roadshow\",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,roadshowList:[],title:\"\",open:!1,queryParams:{pageNum:1,pageSize:10,uniqueCode:null,title:null,status:null},form:{},rules:{uniqueCode:[{required:!0,message:\"唯一标识不能为空\",trigger:\"blur\"},{min:1,max:50,message:\"唯一标识长度必须介于 1 和 50 之间\",trigger:\"blur\"},{validator:this.validateUniqueCode,trigger:\"blur\"}],title:[{required:!0,message:\"标题不能为空\",trigger:\"blur\"},{min:1,max:200,message:\"标题长度必须介于 1 和 200 之间\",trigger:\"blur\"}],status:[{required:!0,message:\"状态不能为空\",trigger:\"change\"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.roadshowList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,uniqueCode:null,coverImage:null,title:null,description:null,status:\"0\",sortOrder:0,remark:null},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加创赛路演管理\"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title=\"修改创赛路演管理\"}))},submitForm:function(){var e=this;this.$refs[\"form\"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess(\"修改成功\"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess(\"新增成功\"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除创赛路演管理编号为\"'+a+'\"的数据项？').then((function(){return d(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/roadshow/export\",Object(i[\"a\"])({},this.queryParams),\"roadshow_\".concat((new Date).getTime(),\".xlsx\"))},validateUniqueCode:function(e,t,a){if(t){var r={id:this.form.id,uniqueCode:t};m(r).then((function(e){e.data?a():a(new Error(\"唯一标识已存在\"))})).catch((function(){a(new Error(\"校验失败\"))}))}else a()}}},h=p,f=a(\"2877\"),g=Object(f[\"a\"])(h,r,o,!1,null,null,null);t[\"default\"]=g.exports}}]);", "extractedComments": []}