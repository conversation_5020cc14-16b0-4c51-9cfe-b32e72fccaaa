{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754295969458}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_homemodule", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "homeModuleList", "title", "open", "queryParams", "pageNum", "pageSize", "moduleName", "moduleCode", "linkType", "status", "form", "sortOrderTimer", "rules", "required", "message", "trigger", "min", "max", "validator", "validateModuleCode", "moduleUrl", "validateModuleUrl", "externalUrl", "validateExternalUrl", "created", "getList", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "methods", "_this", "listHomeModule", "then", "response", "rows", "cancel", "reset", "id", "moduleIcon", "sortOrder", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getHomeModule", "submitForm", "_this3", "$refs", "validate", "valid", "updateHomeModule", "$modal", "msgSuccess", "addHomeModule", "handleDelete", "_this4", "confirm", "delHomeModule", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleLinkTypeChange", "value", "handleSortOrderChange", "_this5", "setTimeout", "updateData", "msgError", "rule", "callback", "checkModuleCodeUnique", "Error", "startsWith", "test"], "sources": ["src/views/miniapp/config/homemodule/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"模块名称\" prop=\"moduleName\">\n        <el-input\n          v-model=\"queryParams.moduleName\"\n          placeholder=\"请输入模块名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"模块代码\" prop=\"moduleCode\">\n        <el-input\n          v-model=\"queryParams.moduleCode\"\n          placeholder=\"请输入模块代码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"链接类型\" prop=\"linkType\">\n        <el-select v-model=\"queryParams.linkType\" placeholder=\"请选择链接类型\" clearable>\n          <el-option label=\"内部页面\" value=\"1\" />\n          <el-option label=\"外部链接\" value=\"2\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable>\n          <el-option label=\"正常\" value=\"0\" />\n          <el-option label=\"停用\" value=\"1\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['config:homemodule:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['config:homemodule:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['config:homemodule:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['config:homemodule:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"homeModuleList\" @selection-change=\"handleSelectionChange\" style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"70\" />\n      <el-table-column label=\"模块图标\" align=\"center\" prop=\"moduleIcon\" width=\"90\">\n        <template slot-scope=\"scope\">\n          <image-preview v-if=\"scope.row.moduleIcon\" :src=\"scope.row.moduleIcon\" :width=\"50\" :height=\"50\"/>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"模块名称\" align=\"center\" prop=\"moduleName\" width=\"120\" show-overflow-tooltip />\n      <el-table-column label=\"模块代码\" align=\"center\" prop=\"moduleCode\" width=\"150\" show-overflow-tooltip />\n      <el-table-column label=\"链接类型\" align=\"center\" prop=\"linkType\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.linkType === '1'\" type=\"primary\" size=\"mini\">内部页面</el-tag>\n          <el-tag v-else-if=\"scope.row.linkType === '2'\" type=\"warning\" size=\"mini\">外部链接</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"跳转地址\" align=\"center\" show-overflow-tooltip min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.linkType === '1'\">{{ scope.row.moduleUrl || '-' }}</span>\n          <span v-else-if=\"scope.row.linkType === '2'\">{{ scope.row.externalUrl || '-' }}</span>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序\" align=\"center\" prop=\"sortOrder\" width=\"110\">\n        <template slot-scope=\"scope\">\n          <el-input-number\n            v-model=\"scope.row.sortOrder\"\n            :min=\"0\"\n            :max=\"9999\"\n            size=\"mini\"\n            controls-position=\"right\"\n            @change=\"handleSortOrderChange(scope.row)\"\n            style=\"width: 90px;\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"90\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.status === '0'\" type=\"success\" size=\"mini\">正常</el-tag>\n          <el-tag v-else type=\"danger\" size=\"mini\">停用</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" width=\"130\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['config:homemodule:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['config:homemodule:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改小程序首页功能模块对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"模块名称\" prop=\"moduleName\">\n          <el-input v-model=\"form.moduleName\" placeholder=\"请输入模块名称\" maxlength=\"100\" />\n        </el-form-item>\n        <el-form-item label=\"模块代码\" prop=\"moduleCode\">\n          <el-input\n            v-model=\"form.moduleCode\"\n            placeholder=\"请输入模块代码\"\n            maxlength=\"50\"\n            :disabled=\"form.id != null\"\n          />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            模块代码用于系统内部识别，必须唯一，创建后不可修改\n          </div>\n        </el-form-item>\n        <el-form-item label=\"模块图标\" prop=\"moduleIcon\">\n          <image-upload v-model=\"form.moduleIcon\"/>\n        </el-form-item>\n        <el-form-item label=\"链接类型\" prop=\"linkType\">\n          <el-radio-group v-model=\"form.linkType\" @change=\"handleLinkTypeChange\">\n            <el-radio label=\"1\">内部页面</el-radio>\n            <el-radio label=\"2\">外部链接</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item v-if=\"form.linkType === '1'\" label=\"内部链接\" prop=\"moduleUrl\">\n          <el-input v-model=\"form.moduleUrl\" placeholder=\"请输入小程序页面路径，如：/pages/home/<USER>\" />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            小程序内部页面路径，以 / 开头\n          </div>\n        </el-form-item>\n        <el-form-item v-if=\"form.linkType === '2'\" label=\"外部链接\" prop=\"externalUrl\">\n          <el-input v-model=\"form.externalUrl\" placeholder=\"请输入完整的URL地址，如：https://www.example.com\" />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            外部网站链接，需要包含 http:// 或 https://\n          </div>\n        </el-form-item>\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\n          <el-input-number v-model=\"form.sortOrder\" :min=\"0\" :max=\"9999\" />\n          <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\n            数字越小越靠前显示\n          </div>\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio label=\"0\">正常</el-radio>\n            <el-radio label=\"1\">停用</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listHomeModule, getHomeModule, delHomeModule, addHomeModule, updateHomeModule, checkModuleCodeUnique } from \"@/api/miniapp/homemodule\";\n\nexport default {\n  name: \"HomeModule\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 小程序首页功能模块表格数据\n      homeModuleList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        moduleName: null,\n        moduleCode: null,\n        linkType: null,\n        status: null\n      },\n      // 表单参数\n      form: {},\n      // 排序防抖定时器\n      sortOrderTimer: null,\n      // 表单校验\n      rules: {\n        moduleName: [\n          { required: true, message: \"模块名称不能为空\", trigger: \"blur\" },\n          { min: 1, max: 100, message: \"模块名称长度必须介于 1 和 100 之间\", trigger: \"blur\" }\n        ],\n        moduleCode: [\n          { required: true, message: \"模块代码不能为空\", trigger: \"blur\" },\n          { min: 1, max: 50, message: \"模块代码长度必须介于 1 和 50 之间\", trigger: \"blur\" },\n          { validator: this.validateModuleCode, trigger: \"blur\" }\n        ],\n        linkType: [\n          { required: true, message: \"链接类型不能为空\", trigger: \"change\" }\n        ],\n        moduleUrl: [\n          { validator: this.validateModuleUrl, trigger: \"blur\" }\n        ],\n        externalUrl: [\n          { validator: this.validateExternalUrl, trigger: \"blur\" }\n        ],\n        status: [\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  beforeDestroy() {\n    // 清理定时器\n    if (this.sortOrderTimer) {\n      clearTimeout(this.sortOrderTimer);\n    }\n  },\n  methods: {\n    /** 查询小程序首页功能模块列表 */\n    getList() {\n      this.loading = true;\n      listHomeModule(this.queryParams).then(response => {\n        this.homeModuleList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        moduleName: null,\n        moduleIcon: null,\n        moduleCode: null,\n        moduleUrl: null,\n        linkType: \"1\",\n        externalUrl: null,\n        sortOrder: 0,\n        status: \"0\",\n        remark: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加小程序首页功能模块\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getHomeModule(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改小程序首页功能模块\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateHomeModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addHomeModule(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除小程序首页功能模块编号为\"' + ids + '\"的数据项？').then(function() {\n        return delHomeModule(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('miniapp/homemodule/export', {\n        ...this.queryParams\n      }, `homemodule_${new Date().getTime()}.xlsx`)\n    },\n    /** 链接类型改变处理 */\n    handleLinkTypeChange(value) {\n      // 清空相关字段\n      if (value === '1') {\n        this.form.externalUrl = null;\n      } else if (value === '2') {\n        this.form.moduleUrl = null;\n      }\n    },\n    /** 排序值改变处理 */\n    handleSortOrderChange(row) {\n      // 防抖处理，避免频繁请求\n      if (this.sortOrderTimer) {\n        clearTimeout(this.sortOrderTimer);\n      }\n      this.sortOrderTimer = setTimeout(() => {\n        const updateData = {\n          id: row.id,\n          moduleName: row.moduleName,\n          moduleIcon: row.moduleIcon,\n          moduleCode: row.moduleCode,\n          moduleUrl: row.moduleUrl,\n          linkType: row.linkType,\n          externalUrl: row.externalUrl,\n          sortOrder: row.sortOrder,\n          status: row.status,\n          remark: row.remark\n        };\n        updateHomeModule(updateData).then(() => {\n          this.$modal.msgSuccess(\"排序更新成功\");\n          this.getList(); // 重新加载列表以显示最新排序\n        }).catch(() => {\n          this.$modal.msgError(\"排序更新失败\");\n          this.getList(); // 失败时也重新加载，恢复原始数据\n        });\n      }, 800); // 800ms防抖\n    },\n    /** 模块代码校验 */\n    validateModuleCode(rule, value, callback) {\n      if (value) {\n        const data = {\n          id: this.form.id,\n          moduleCode: value\n        };\n        checkModuleCodeUnique(data).then(response => {\n          if (response.data) {\n            callback();\n          } else {\n            callback(new Error(\"模块代码已存在\"));\n          }\n        }).catch(() => {\n          callback(new Error(\"校验失败\"));\n        });\n      } else {\n        callback();\n      }\n    },\n    /** 内部链接校验 */\n    validateModuleUrl(rule, value, callback) {\n      if (this.form.linkType === '1') {\n        if (!value) {\n          callback(new Error(\"内部链接不能为空\"));\n        } else if (!value.startsWith('/')) {\n          callback(new Error(\"内部链接必须以 / 开头\"));\n        } else {\n          callback();\n        }\n      } else {\n        callback();\n      }\n    },\n    /** 外部链接校验 */\n    validateExternalUrl(rule, value, callback) {\n      if (this.form.linkType === '2') {\n        if (!value) {\n          callback(new Error(\"外部链接不能为空\"));\n        } else if (!/^https?:\\/\\/.+/.test(value)) {\n          callback(new Error(\"外部链接必须以 http:// 或 https:// 开头\"));\n        } else {\n          callback();\n        }\n      } else {\n        callback();\n      }\n    }\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;AAyNA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;QACAN,UAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,UAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,SAAA,OAAAC,kBAAA;UAAAJ,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,SAAA,GACA;UAAAF,SAAA,OAAAG,iBAAA;UAAAN,OAAA;QAAA,EACA;QACAO,WAAA,GACA;UAAAJ,SAAA,OAAAK,mBAAA;UAAAR,OAAA;QAAA,EACA;QACAN,MAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAf,cAAA;MACAgB,YAAA,MAAAhB,cAAA;IACA;EACA;EACAiB,OAAA;IACA,oBACAH,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAAnC,OAAA;MACA,IAAAoC,0BAAA,OAAA3B,WAAA,EAAA4B,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA7B,cAAA,GAAAgC,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA9B,KAAA,GAAAiC,QAAA,CAAAjC,KAAA;QACA8B,KAAA,CAAAnC,OAAA;MACA;IACA;IACA;IACAwC,MAAA,WAAAA,OAAA;MACA,KAAAhC,IAAA;MACA,KAAAiC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAzB,IAAA;QACA0B,EAAA;QACA9B,UAAA;QACA+B,UAAA;QACA9B,UAAA;QACAa,SAAA;QACAZ,QAAA;QACAc,WAAA;QACAgB,SAAA;QACA7B,MAAA;QACA8B,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAC,OAAA;MACA,KAAAqB,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjD,GAAA,GAAAiD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAV,EAAA;MAAA;MACA,KAAAxC,MAAA,GAAAgD,SAAA,CAAAG,MAAA;MACA,KAAAlD,QAAA,IAAA+C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAAjC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA;MACA,IAAAC,EAAA,GAAAc,GAAA,CAAAd,EAAA,SAAAzC,GAAA;MACA,IAAAyD,yBAAA,EAAAhB,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAmB,MAAA,CAAAzC,IAAA,GAAAsB,QAAA,CAAAvC,IAAA;QACA0D,MAAA,CAAAjD,IAAA;QACAiD,MAAA,CAAAlD,KAAA;MACA;IACA;IACA,WACAoD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA5C,IAAA,CAAA0B,EAAA;YACA,IAAAsB,4BAAA,EAAAJ,MAAA,CAAA5C,IAAA,EAAAqB,IAAA,WAAAC,QAAA;cACAsB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAApD,IAAA;cACAoD,MAAA,CAAA7B,OAAA;YACA;UACA;YACA,IAAAoC,yBAAA,EAAAP,MAAA,CAAA5C,IAAA,EAAAqB,IAAA,WAAAC,QAAA;cACAsB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAApD,IAAA;cACAoD,MAAA,CAAA7B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAApE,GAAA,GAAAuD,GAAA,CAAAd,EAAA,SAAAzC,GAAA;MACA,KAAAgE,MAAA,CAAAK,OAAA,yBAAArE,GAAA,aAAAoC,IAAA;QACA,WAAAkC,yBAAA,EAAAtE,GAAA;MACA,GAAAoC,IAAA;QACAgC,MAAA,CAAAtC,OAAA;QACAsC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,kCAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnE,WAAA,iBAAAoE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,eACAC,oBAAA,WAAAA,qBAAAC,KAAA;MACA;MACA,IAAAA,KAAA;QACA,KAAAjE,IAAA,CAAAY,WAAA;MACA,WAAAqD,KAAA;QACA,KAAAjE,IAAA,CAAAU,SAAA;MACA;IACA;IACA,cACAwD,qBAAA,WAAAA,sBAAA1B,GAAA;MAAA,IAAA2B,MAAA;MACA;MACA,SAAAlE,cAAA;QACAgB,YAAA,MAAAhB,cAAA;MACA;MACA,KAAAA,cAAA,GAAAmE,UAAA;QACA,IAAAC,UAAA;UACA3C,EAAA,EAAAc,GAAA,CAAAd,EAAA;UACA9B,UAAA,EAAA4C,GAAA,CAAA5C,UAAA;UACA+B,UAAA,EAAAa,GAAA,CAAAb,UAAA;UACA9B,UAAA,EAAA2C,GAAA,CAAA3C,UAAA;UACAa,SAAA,EAAA8B,GAAA,CAAA9B,SAAA;UACAZ,QAAA,EAAA0C,GAAA,CAAA1C,QAAA;UACAc,WAAA,EAAA4B,GAAA,CAAA5B,WAAA;UACAgB,SAAA,EAAAY,GAAA,CAAAZ,SAAA;UACA7B,MAAA,EAAAyC,GAAA,CAAAzC,MAAA;UACA8B,MAAA,EAAAW,GAAA,CAAAX;QACA;QACA,IAAAmB,4BAAA,EAAAqB,UAAA,EAAAhD,IAAA;UACA8C,MAAA,CAAAlB,MAAA,CAAAC,UAAA;UACAiB,MAAA,CAAApD,OAAA;QACA,GAAAyC,KAAA;UACAW,MAAA,CAAAlB,MAAA,CAAAqB,QAAA;UACAH,MAAA,CAAApD,OAAA;QACA;MACA;IACA;IACA,aACAN,kBAAA,WAAAA,mBAAA8D,IAAA,EAAAN,KAAA,EAAAO,QAAA;MACA,IAAAP,KAAA;QACA,IAAAlF,IAAA;UACA2C,EAAA,OAAA1B,IAAA,CAAA0B,EAAA;UACA7B,UAAA,EAAAoE;QACA;QACA,IAAAQ,iCAAA,EAAA1F,IAAA,EAAAsC,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAvC,IAAA;YACAyF,QAAA;UACA;YACAA,QAAA,KAAAE,KAAA;UACA;QACA,GAAAlB,KAAA;UACAgB,QAAA,KAAAE,KAAA;QACA;MACA;QACAF,QAAA;MACA;IACA;IACA,aACA7D,iBAAA,WAAAA,kBAAA4D,IAAA,EAAAN,KAAA,EAAAO,QAAA;MACA,SAAAxE,IAAA,CAAAF,QAAA;QACA,KAAAmE,KAAA;UACAO,QAAA,KAAAE,KAAA;QACA,YAAAT,KAAA,CAAAU,UAAA;UACAH,QAAA,KAAAE,KAAA;QACA;UACAF,QAAA;QACA;MACA;QACAA,QAAA;MACA;IACA;IACA,aACA3D,mBAAA,WAAAA,oBAAA0D,IAAA,EAAAN,KAAA,EAAAO,QAAA;MACA,SAAAxE,IAAA,CAAAF,QAAA;QACA,KAAAmE,KAAA;UACAO,QAAA,KAAAE,KAAA;QACA,6BAAAE,IAAA,CAAAX,KAAA;UACAO,QAAA,KAAAE,KAAA;QACA;UACAF,QAAA;QACA;MACA;QACAA,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}