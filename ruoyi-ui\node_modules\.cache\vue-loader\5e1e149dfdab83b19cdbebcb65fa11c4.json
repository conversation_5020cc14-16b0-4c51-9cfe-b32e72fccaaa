{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\test\\ImageUploadTest.vue?vue&type=style&index=0&id=77097ceb&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\test\\ImageUploadTest.vue", "mtime": 1754297883203}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoudGVzdC1jb250YWluZXIgewogIHBhZGRpbmc6IDIwcHg7CiAgbWF4LXdpZHRoOiA4MDBweDsKICBtYXJnaW46IDAgYXV0bzsKfQoKLnRlc3Qtc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7Cn0KCi50ZXN0LXNlY3Rpb24gaDMgewogIG1hcmdpbi10b3A6IDA7CiAgY29sb3I6ICM0MDllZmY7Cn0KCi50ZXN0LXNlY3Rpb24gcCB7CiAgbWFyZ2luOiAxMHB4IDA7CiAgZm9udC1zaXplOiAxNHB4Owp9Cg=="}, {"version": 3, "sources": ["ImageUploadTest.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ImageUploadTest.vue", "sourceRoot": "src/views/test", "sourcesContent": ["<template>\n  <div class=\"test-container\">\n    <h2>ImageUpload组件测试</h2>\n    \n    <div class=\"test-section\">\n      <h3>测试场景1：相对路径（应该自动添加完整URL前缀）</h3>\n      <p>测试值：/profile/upload/2025/08/04/icon7_20250804164314A007.png</p>\n      <ImageUpload v-model=\"testValue1\" :limit=\"1\" />\n      <p>当前值：{{ testValue1 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试场景2：已包含baseUrl的路径</h3>\n      <p>测试值：/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png</p>\n      <ImageUpload v-model=\"testValue2\" :limit=\"1\" />\n      <p>当前值：{{ testValue2 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试场景3：完整URL</h3>\n      <p>测试值：http://localhost:8086/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png</p>\n      <ImageUpload v-model=\"testValue3\" :limit=\"1\" />\n      <p>当前值：{{ testValue3 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>测试场景4：外部URL</h3>\n      <p>测试值：https://example.com/image.jpg</p>\n      <ImageUpload v-model=\"testValue4\" :limit=\"1\" />\n      <p>当前值：{{ testValue4 }}</p>\n    </div>\n\n    <div class=\"test-section\">\n      <h3>URL构建测试</h3>\n      <p>当前协议：{{ currentProtocol }}</p>\n      <p>当前主机：{{ currentHost }}</p>\n      <p>API前缀：{{ apiPrefix }}</p>\n      <p>构建的完整URL示例：{{ fullUrlExample }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ImageUpload from '@/components/ImageUpload'\n\nexport default {\n  name: 'ImageUploadTest',\n  components: {\n    ImageUpload\n  },\n  data() {\n    return {\n      testValue1: '/profile/upload/2025/08/04/icon7_20250804164314A007.png',\n      testValue2: '/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png',\n      testValue3: 'http://localhost:8086/dev-api/profile/upload/2025/08/04/icon7_20250804164314A007.png',\n      testValue4: 'https://example.com/image.jpg'\n    }\n  },\n  computed: {\n    currentProtocol() {\n      return window.location.protocol\n    },\n    currentHost() {\n      return window.location.host\n    },\n    apiPrefix() {\n      return process.env.VUE_APP_BASE_API\n    },\n    fullUrlExample() {\n      const testPath = '/profile/upload/2025/08/04/icon7_20250804164314A007.png'\n      return window.location.protocol + '//' + window.location.host + process.env.VUE_APP_BASE_API + testPath\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background-color: #fafafa;\n}\n\n.test-section h3 {\n  margin-top: 0;\n  color: #409eff;\n}\n\n.test-section p {\n  margin: 10px 0;\n  font-size: 14px;\n}\n</style>\n"]}]}