# 小程序码使用示例

## 前端调用示例

### 1. 获取活动小程序码

```javascript
// 获取指定活动的小程序码
async function getEventQRCode(eventId) {
    try {
        const response = await axios.post('/miniapp/event/getQRCode', {
            eventId: eventId
        }, {
            headers: {
                'Authorization': 'Bearer ' + getToken(),
                'Content-Type': 'application/json'
            }
        });

        if (response.data.code === 200) {
            const result = response.data.data;
            console.log('活动标题:', result.eventTitle);
            
            // 显示二维码图片
            const imgElement = document.getElementById('qrcode-img');
            imgElement.src = result.qrcode;
            
            // 或者在Vue中使用
            this.qrcodeUrl = result.qrcode;
            
            return result;
        } else {
            throw new Error(response.data.msg);
        }
    } catch (error) {
        console.error('获取小程序码失败:', error);
        throw error;
    }
}
```

### 2. 获取自定义小程序码

```javascript
// 生成自定义参数的小程序码
async function getCustomQRCode(options) {
    const defaultOptions = {
        path: 'pages/event/detail?eventId=123',
        width: 430,
        autoColor: false,
        lineColor: { r: 0, g: 0, b: 0 },
        isHyaline: false,
        envVersion: 'release'
    };
    
    const params = { ...defaultOptions, ...options };
    
    try {
        const response = await axios.post('/miniapp/event/getCustomQRCode', params, {
            headers: {
                'Authorization': 'Bearer ' + getToken(),
                'Content-Type': 'application/json'
            }
        });

        if (response.data.code === 200) {
            return response.data.data.qrcode;
        } else {
            throw new Error(response.data.msg);
        }
    } catch (error) {
        console.error('生成自定义小程序码失败:', error);
        throw error;
    }
}
```

## Vue组件示例

```vue
<template>
  <div class="qrcode-generator">
    <el-card>
      <div slot="header">
        <span>活动小程序码</span>
      </div>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="活动ID">
          <el-input v-model="form.eventId" placeholder="请输入活动ID"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="generateQRCode" :loading="loading">
            生成小程序码
          </el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="qrcodeData" class="qrcode-result">
        <h4>{{ qrcodeData.eventTitle }}</h4>
        <img :src="qrcodeData.qrcode" alt="小程序码" class="qrcode-img">
        <div class="qrcode-actions">
          <el-button @click="downloadQRCode">下载二维码</el-button>
          <el-button @click="copyQRCode">复制图片</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getEventQRCode } from '@/api/miniapp/event'

export default {
  name: 'QRCodeGenerator',
  data() {
    return {
      form: {
        eventId: ''
      },
      loading: false,
      qrcodeData: null
    }
  },
  methods: {
    async generateQRCode() {
      if (!this.form.eventId) {
        this.$message.warning('请输入活动ID');
        return;
      }
      
      this.loading = true;
      try {
        const result = await getEventQRCode(this.form.eventId);
        this.qrcodeData = result;
        this.$message.success('小程序码生成成功');
      } catch (error) {
        this.$message.error('生成失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },
    
    downloadQRCode() {
      if (!this.qrcodeData) return;
      
      const link = document.createElement('a');
      link.href = this.qrcodeData.qrcode;
      link.download = `活动${this.qrcodeData.eventId}_小程序码.png`;
      link.click();
    },
    
    async copyQRCode() {
      if (!this.qrcodeData) return;
      
      try {
        // 将Base64转换为Blob
        const response = await fetch(this.qrcodeData.qrcode);
        const blob = await response.blob();
        
        // 复制到剪贴板
        await navigator.clipboard.write([
          new ClipboardItem({ 'image/png': blob })
        ]);
        
        this.$message.success('图片已复制到剪贴板');
      } catch (error) {
        this.$message.error('复制失败: ' + error.message);
      }
    }
  }
}
</script>

<style scoped>
.qrcode-result {
  text-align: center;
  margin-top: 20px;
}

.qrcode-img {
  max-width: 300px;
  margin: 20px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.qrcode-actions {
  margin-top: 15px;
}
</style>
```

## API调用示例

```javascript
// api/miniapp/event.js
import request from '@/utils/request'

// 获取活动小程序码
export function getEventQRCode(eventId) {
  return request({
    url: '/miniapp/event/getQRCode',
    method: 'post',
    data: { eventId }
  })
}

// 获取自定义小程序码
export function getCustomQRCode(params) {
  return request({
    url: '/miniapp/event/getCustomQRCode',
    method: 'post',
    data: params
  })
}
```

## 使用注意事项

1. **权限配置**：确保用户具有 `miniapp:event:qrcode` 权限
2. **微信配置**：确保微信小程序配置正确且有效
3. **图片处理**：返回的是Base64格式，可直接用于img标签
4. **错误处理**：注意处理网络错误和业务错误
5. **性能考虑**：避免频繁生成，可考虑缓存机制
