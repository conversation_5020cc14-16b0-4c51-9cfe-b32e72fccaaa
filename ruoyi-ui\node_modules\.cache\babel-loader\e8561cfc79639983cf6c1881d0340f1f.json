{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\components\\ImageUpload\\index.vue", "mtime": 1754297908532}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}