{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\homemodule.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\homemodule.js", "mtime": 1754295207319}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9yb290L0Rlc2t0b3AvXHU5ODc5XHU3NkVFXHU4QkIwXHU1RjU1XHVGRjA4XHU1NDM0XHU5Rjk5XHU5Rjk5XHVGRjA5L3RqdWhhaXRhbmdfbWluaWFwcC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkSG9tZU1vZHVsZSA9IGFkZEhvbWVNb2R1bGU7CmV4cG9ydHMuY2hlY2tNb2R1bGVDb2RlVW5pcXVlID0gY2hlY2tNb2R1bGVDb2RlVW5pcXVlOwpleHBvcnRzLmRlbEhvbWVNb2R1bGUgPSBkZWxIb21lTW9kdWxlOwpleHBvcnRzLmdldEhvbWVNb2R1bGUgPSBnZXRIb21lTW9kdWxlOwpleHBvcnRzLmdldEhvbWVNb2R1bGVCeUNvZGUgPSBnZXRIb21lTW9kdWxlQnlDb2RlOwpleHBvcnRzLmdldE1vZHVsZUxpc3RGb3JBcHAgPSBnZXRNb2R1bGVMaXN0Rm9yQXBwOwpleHBvcnRzLmxpc3RIb21lTW9kdWxlID0gbGlzdEhvbWVNb2R1bGU7CmV4cG9ydHMudXBkYXRlSG9tZU1vZHVsZSA9IHVwZGF0ZUhvbWVNb2R1bGU7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LlsI/nqIvluo/pppbpobXlip/og73mqKHlnZfliJfooagKZnVuY3Rpb24gbGlzdEhvbWVNb2R1bGUocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hvbWVtb2R1bGUvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LlsI/nqIvluo/pppbpobXlip/og73mqKHlnZfor6bnu4YKZnVuY3Rpb24gZ2V0SG9tZU1vZHVsZShpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21pbmlhcHAvaG9tZW1vZHVsZS8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdlwpmdW5jdGlvbiBhZGRIb21lTW9kdWxlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hvbWVtb2R1bGUnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueWwj+eoi+W6j+mmlumhteWKn+iDveaooeWdlwpmdW5jdGlvbiB1cGRhdGVIb21lTW9kdWxlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hvbWVtb2R1bGUnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5bCP56iL5bqP6aaW6aG15Yqf6IO95qih5Z2XCmZ1bmN0aW9uIGRlbEhvbWVNb2R1bGUoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hvbWVtb2R1bGUvJyArIGlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovLyDmoKHpqozmqKHlnZfku6PnoIEKZnVuY3Rpb24gY2hlY2tNb2R1bGVDb2RlVW5pcXVlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hvbWVtb2R1bGUvY2hlY2tNb2R1bGVDb2RlVW5pcXVlJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDojrflj5blsI/nqIvluo/pppbpobXlip/og73mqKHlnZfliJfooajvvIjlsI/nqIvluo/nq6/vvIkKZnVuY3Rpb24gZ2V0TW9kdWxlTGlzdEZvckFwcCgpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hvbWVtb2R1bGUvYXBwL2dldE1vZHVsZUxpc3QnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmoLnmja7mqKHlnZfku6PnoIHojrflj5bmqKHlnZfor6bmg4XvvIjlsI/nqIvluo/nq6/vvIkKZnVuY3Rpb24gZ2V0SG9tZU1vZHVsZUJ5Q29kZShtb2R1bGVDb2RlKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbWluaWFwcC9ob21lbW9kdWxlL2FwcC9nZXRCeUNvZGUvJyArIG1vZHVsZUNvZGUsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listHomeModule", "query", "request", "url", "method", "params", "getHomeModule", "id", "addHomeModule", "data", "updateHomeModule", "delHomeModule", "checkModuleCodeUnique", "getModuleListForApp", "getHomeModuleByCode", "moduleCode"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/homemodule.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询小程序首页功能模块列表\nexport function listHomeModule(query) {\n  return request({\n    url: '/miniapp/homemodule/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询小程序首页功能模块详细\nexport function getHomeModule(id) {\n  return request({\n    url: '/miniapp/homemodule/' + id,\n    method: 'get'\n  })\n}\n\n// 新增小程序首页功能模块\nexport function addHomeModule(data) {\n  return request({\n    url: '/miniapp/homemodule',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改小程序首页功能模块\nexport function updateHomeModule(data) {\n  return request({\n    url: '/miniapp/homemodule',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除小程序首页功能模块\nexport function delHomeModule(id) {\n  return request({\n    url: '/miniapp/homemodule/' + id,\n    method: 'delete'\n  })\n}\n\n// 校验模块代码\nexport function checkModuleCodeUnique(data) {\n  return request({\n    url: '/miniapp/homemodule/checkModuleCodeUnique',\n    method: 'post',\n    data: data\n  })\n}\n\n// 获取小程序首页功能模块列表（小程序端）\nexport function getModuleListForApp() {\n  return request({\n    url: '/miniapp/homemodule/app/getModuleList',\n    method: 'get'\n  })\n}\n\n// 根据模块代码获取模块详情（小程序端）\nexport function getHomeModuleByCode(moduleCode) {\n  return request({\n    url: '/miniapp/homemodule/app/getByCode/' + moduleCode,\n    method: 'get'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,EAAE;IAChCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,qBAAqBA,CAACH,IAAI,EAAE;EAC1C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,mBAAmBA,CAAA,EAAG;EACpC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,mBAAmBA,CAACC,UAAU,EAAE;EAC9C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC,GAAGY,UAAU;IACtDX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}