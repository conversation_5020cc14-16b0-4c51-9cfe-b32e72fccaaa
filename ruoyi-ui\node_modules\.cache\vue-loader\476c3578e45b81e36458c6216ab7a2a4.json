{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue?vue&type=style&index=0&id=74da65c0&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\demandcategory\\index.vue", "mtime": 1754298434316}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5mb3JtLWNvbmZpZy1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBoZWlnaHQ6IDcwMHB4Ow0KICBnYXA6IDIwcHg7DQp9DQoNCi5jb25maWctYXJlYSB7DQogIGZsZXg6IDE7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlNmU2ZTY7DQogIHBhZGRpbmctcmlnaHQ6IDIwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5wcmV2aWV3LWFyZWEgew0KICBmbGV4OiAxOw0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5jb25maWctaGVhZGVyLCAucHJldmlldy1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIHBhZGRpbmctYm90dG9tOiAxMHB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U2ZTZlNjsNCn0NCg0KLmNvbmZpZy1oZWFkZXIgaDQsIC5wcmV2aWV3LWhlYWRlciBoNCB7DQogIG1hcmdpbjogMDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5oZWFkZXItYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTBweDsNCn0NCg0KLnRlbXBsYXRlLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoubW9kdWxlcy1saXN0IHsNCiAgbWF4LWhlaWdodDogNjAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5tb2R1bGUtaXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5tb2R1bGUtY2FyZCB7DQogIGJvcmRlcjogMnB4IHNvbGlkICNlNmU2ZTY7DQogIHRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjNzOw0KfQ0KDQoubW9kdWxlLWNhcmQ6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5tb2R1bGUtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBwYWRkaW5nLWJvdHRvbTogMTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5tb2R1bGUtdGl0bGUtc2VjdGlvbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGZsZXg6IDE7DQp9DQoNCi5tb2R1bGUtaWNvbiB7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBmb250LXNpemU6IDE4cHg7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KfQ0KDQoubW9kdWxlLW5hbWUtaW5wdXQgew0KICBtYXgtd2lkdGg6IDIwMHB4Ow0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi5tb2R1bGUtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogNXB4Ow0KICBmbGV4LXNocmluazogMDsNCn0NCg0KDQoNCi5maWVsZHMtbGlzdCB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQp9DQoNCi5maWVsZC1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmZpZWxkLWNhcmQgew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTZlNmU2Ow0KICBtYXJnaW4tbGVmdDogMjBweDsNCn0NCg0KLmZpZWxkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmZpZWxkLXRpdGxlIHsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouZmllbGQtYWN0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogNXB4Ow0KfQ0KDQouZW1wdHktbW9kdWxlIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KLmVtcHR5LW1vZHVsZXMgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDQwcHg7DQp9DQoNCi5wcmV2aWV3LWNvbnRlbnQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmOWY5Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG1pbi1oZWlnaHQ6IDUwMHB4Ow0KfQ0KDQoucHJldmlldy1tb2R1bGUgew0KICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KfQ0KDQoucHJldmlldy1tb2R1bGUtaGVhZGVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZy1ib3R0b206IDEwcHg7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjNDA5RUZGOw0KfQ0KDQoucHJldmlldy1tb2R1bGUtaGVhZGVyIGg1IHsNCiAgbWFyZ2luOiAwIDAgNXB4IDA7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQoNCg0KLnByZXZpZXctZW1wdHktbW9kdWxlIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogI2MwYzRjYzsNCiAgcGFkZGluZzogMjBweDsNCiAgZm9udC1zdHlsZTogaXRhbGljOw0KfQ0KDQoucHJldmlldy1lbXB0eSB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogNDBweDsNCn0NCg0KLyog6ZqQ6JeP5a2X5q615qC35byPICovDQouaGlkZGVuLWZpZWxkIHsNCiAgb3BhY2l0eTogMC42Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIHBhZGRpbmc6IDhweDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBib3JkZXItbGVmdDogM3B4IHNvbGlkICM5MDkzOTk7DQp9DQoNCi5oaWRkZW4tZmllbGQtaW5kaWNhdG9yIHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQouZmllbGQtdGlwIHsNCiAgbWFyZ2luLXRvcDogNXB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouZmllbGQtdGlwIGkgew0KICBtYXJnaW4tcmlnaHQ6IDRweDsNCn0NCg0KLyog6KGo5Y2V5a+56K+d5qGG5qC35byP5LyY5YyWICovDQouZWwtZGlhbG9nX19ib2R5IHsNCiAgcGFkZGluZzogMjBweCAzMHB4Ow0KfQ0KDQouZWwtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMThweDsNCn0NCg0KLmVsLWlucHV0LW51bWJlciB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQovKiDpooTop4jlr7nor53moYbmoLflvI8gKi8NCi5wcmV2aWV3LWRpYWxvZy1jb250ZW50IHsNCiAgbWF4LWhlaWdodDogNTAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5wcmV2aWV3LWRpYWxvZy1tb2R1bGUgew0KICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTZlNmU2Ow0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7DQp9DQoNCi5wcmV2aWV3LWRpYWxvZy1tb2R1bGUtaGVhZGVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZy1ib3R0b206IDEwcHg7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjNDA5RUZGOw0KfQ0KDQoucHJldmlldy1kaWFsb2ctbW9kdWxlLWhlYWRlciBoNCB7DQogIG1hcmdpbjogMCAwIDVweCAwOw0KICBjb2xvcjogIzQwOUVGRjsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQoNCg0KLnByZXZpZXctZGlhbG9nLWVtcHR5LW1vZHVsZSB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICNjMGM0Y2M7DQogIHBhZGRpbmc6IDIwcHg7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCn0NCg0KLnByZXZpZXctZGlhbG9nLWVtcHR5IHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiA0MHB4Ow0KfQ0KDQouZWwtY2FyZCB7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5lbC1mb3JtLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQovKiDpnZnmgIHlsZXnpLrlhoXlrrnmoLflvI8gKi8NCi5zdGF0aWMtY29udGVudCB7DQogIHBhZGRpbmc6IDEwcHggMTVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCiAgbGluZS1oZWlnaHQ6IDEuNjsNCiAgd2hpdGUtc3BhY2U6IHByZS13cmFwOw0KICB3b3JkLWJyZWFrOiBicmVhay13b3JkOw0KfQ0KDQovKiDmqKHlnZflm77moIfmoLflvI8gKi8NCi5tb2R1bGUtaWNvbi1zZWN0aW9uIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5tb2R1bGUtaWNvbi1kaXNwbGF5IHsNCiAgd2lkdGg6IDMycHg7DQogIGhlaWdodDogMzJweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBtYXJnaW4tcmlnaHQ6IDRweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3M7DQp9DQoNCi5tb2R1bGUtaWNvbi1kaXNwbGF5OmhvdmVyIHsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmOWZmOw0KfQ0KDQouY3VzdG9tLWljb24gew0KICB3aWR0aDogMjRweDsNCiAgaGVpZ2h0OiAyNHB4Ow0KICBvYmplY3QtZml0OiBjb3ZlcjsNCiAgYm9yZGVyLXJhZGl1czogMnB4Ow0KfQ0KDQouZGVmYXVsdC1pY29uLXBsYWNlaG9sZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGZvbnQtc2l6ZTogMTBweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmRlZmF1bHQtaWNvbi1wbGFjZWhvbGRlciBpIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tYm90dG9tOiAycHg7DQp9DQoNCi5kZWZhdWx0LWljb24tcGxhY2Vob2xkZXIgc3BhbiB7DQogIGxpbmUtaGVpZ2h0OiAxOw0KfQ0KDQouaWNvbi1lZGl0LWJ0biB7DQogIHBhZGRpbmc6IDRweCAhaW1wb3J0YW50Ow0KICBtaW4taGVpZ2h0OiBhdXRvICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOWbvuagh+S4iuS8oOWZqOagt+W8jyAqLw0KLmljb24tdXBsb2FkZXItY29udGVudCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnVwbG9hZC1zZWN0aW9uIGg0IHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi51cGxvYWQtdGlwcyB7DQogIG1hcmdpbi10b3A6IDE1cHg7DQogIHRleHQtYWxpZ246IGxlZnQ7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjNDA5ZWZmOw0KfQ0KDQoudXBsb2FkLXRpcHMgcCB7DQogIG1hcmdpbjogNXB4IDA7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQp9DQoNCi8qIOmihOiniOWMuuWfn+Wbvuagh+agt+W8jyAqLw0KLnByZXZpZXctbW9kdWxlLXRpdGxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5wcmV2aWV3LW1vZHVsZS1pY29uIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBjb2xvcjogIzQwOWVmZjsNCn0NCg0KLnByZXZpZXctbW9kdWxlLWljb24taW1nIHsNCiAgd2lkdGg6IDE2cHg7DQogIGhlaWdodDogMTZweDsNCiAgb2JqZWN0LWZpdDogY292ZXI7DQp9DQoNCi5wcmV2aWV3LWRpYWxvZy1tb2R1bGUtdGl0bGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLnByZXZpZXctZGlhbG9nLW1vZHVsZS1pY29uIHsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBjb2xvcjogIzQwOWVmZjsNCn0NCg0KLnByZXZpZXctZGlhbG9nLW1vZHVsZS1pY29uLWltZyB7DQogIHdpZHRoOiAxOHB4Ow0KICBoZWlnaHQ6IDE4cHg7DQogIG9iamVjdC1maXQ6IGNvdmVyOw0KfQ0KDQovKiDnsbvlnovlm77moIfmoLflvI8gKi8NCi5jYXRlZ29yeS1pY29uIHsNCiAgd2lkdGg6IDMycHg7DQogIGhlaWdodDogMzJweDsNCiAgb2JqZWN0LWZpdDogY292ZXI7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCn0NCg0KLm5vLWljb24gew0KICBjb2xvcjogI2MwYzRjYzsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouZm9ybS10aXAgew0KICBtYXJnaW4tdG9wOiA4cHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIGxpbmUtaGVpZ2h0OiAxLjQ7DQp9DQoNCi5mb3JtLXRpcCBwIHsNCiAgbWFyZ2luOiAycHggMDsNCn0NCg0KLmZvcm0tdGlwIHsNCiAgbWFyZ2luLXRvcDogOHB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBsaW5lLWhlaWdodDogMS40Ow0KfQ0KDQouZm9ybS10aXAgcCB7DQogIG1hcmdpbjogMnB4IDA7DQp9DQoNCg=="}, null]}