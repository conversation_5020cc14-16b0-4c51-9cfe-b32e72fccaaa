package com.ruoyi.miniapp.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.miniapp.domain.MiniEvent;
import com.ruoyi.miniapp.service.IMiniEventService;
import com.ruoyi.miniapp.service.WechatMiniappService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 活动报名Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Api(tags = "活动报名管理")
@RestController
@RequestMapping("/miniapp/event")
public class MiniEventController extends BaseController
{
    @Autowired
    private IMiniEventService miniEventService;

    @Autowired
    private WechatMiniappService wechatMiniappService;

    /**
     * 查询活动报名列表
     */
    @ApiOperation("查询活动报名列表")
    @PreAuthorize("@ss.hasPermi('miniapp:event:list')")
    @PostMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") @RequestBody MiniEvent miniEvent)
    {
        startPage();
        List<MiniEvent> list = miniEventService.selectMiniEventList(miniEvent);
        return getDataTable(list);
    }

    /**
     * 导出活动报名列表
     */
    @ApiOperation("导出活动报名列表")
    @PreAuthorize("@ss.hasPermi('miniapp:event:export')")
    @Log(title = "活动报名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") @RequestBody MiniEvent miniEvent)
    {
        List<MiniEvent> list = miniEventService.selectMiniEventList(miniEvent);
        ExcelUtil<MiniEvent> util = new ExcelUtil<MiniEvent>(MiniEvent.class);
        util.exportExcel(response, list, "活动报名数据");
    }

    /**
     * 获取活动报名详细信息
     */
    @ApiOperation("获取活动报名详细信息")
    @PreAuthorize("@ss.hasPermi('miniapp:event:query')")
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@ApiParam("活动ID") @RequestBody Long eventId)
    {
        return AjaxResult.success(miniEventService.selectMiniEventByEventId(eventId));
    }

    /**
     * 新增活动报名
     */
    @ApiOperation("新增活动报名")
    @PreAuthorize("@ss.hasPermi('miniapp:event:add')")
    @Log(title = "活动报名", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@ApiParam("活动信息") @RequestBody MiniEvent miniEvent)
    {
        return toAjax(miniEventService.insertMiniEvent(miniEvent));
    }

    /**
     * 修改活动报名
     */
    @ApiOperation("修改活动报名")
    @PreAuthorize("@ss.hasPermi('miniapp:event:edit')")
    @Log(title = "活动报名", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@ApiParam("活动信息") @RequestBody MiniEvent miniEvent)
    {
        return toAjax(miniEventService.updateMiniEvent(miniEvent));
    }

    /**
     * 删除活动报名
     */
    @ApiOperation("删除活动报名")
    @PreAuthorize("@ss.hasPermi('miniapp:event:remove')")
    @Log(title = "活动报名", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@ApiParam("活动ID数组") @RequestBody Long[] eventIds)
    {
        return toAjax(miniEventService.deleteMiniEventByEventIds(eventIds));
    }

    // ================================ 小程序端接口 ================================

    /**
     * 获取正在进行的活动列表
     */
    @ApiOperation("获取正在进行的活动列表")
    @PostMapping("/app/getActiveList")
    public AjaxResult getActiveList()
    {
        List<MiniEvent> list = miniEventService.selectActiveMiniEventList();
        return AjaxResult.success(list);
    }

    /**
     * 获取启用的活动列表
     */
    @ApiOperation("获取启用的活动列表")
    @PostMapping("/app/getEnabledList")
    public AjaxResult getEnabledList()
    {
        List<MiniEvent> list = miniEventService.selectEnabledMiniEventList();
        return AjaxResult.success(list);
    }

    /**
     * 获取活动详情
     */
    @ApiOperation("获取活动详情")
    @PostMapping("/app/getDetail")
    public AjaxResult getDetail(@ApiParam("活动ID") @RequestBody Long eventId)
    {
        return AjaxResult.success(miniEventService.selectMiniEventByEventId(eventId));
    }

    /**
     * 小程序端搜索活动
     */
    @ApiOperation("小程序端搜索活动")
    @PostMapping("/app/search")
    public AjaxResult searchEvents(@ApiParam("搜索条件") @RequestBody Map<String, Object> params)
    {
        String keyword = (String) params.get("keyword");
        String status = (String) params.get("status"); // all, active, ended
        String eventType = (String) params.get("eventType"); // activity, guidance
        Object userIdObj = params.get("userId");
        Long userId = userIdObj == null ? null : ((Number) userIdObj).longValue();

        List<MiniEvent> list = miniEventService.searchEventsForApp(keyword, status, eventType, userId);
        return AjaxResult.success(list);
    }

    /**
     * 获取用户参与的活动列表
     */
    @ApiOperation("获取用户参与的活动列表")
    @PostMapping("/app/getUserEvents")
    public AjaxResult getUserEvents(@ApiParam("用户ID") @RequestBody Long userId)
    {
        List<MiniEvent> list = miniEventService.selectEventsByUserId(userId);
        return AjaxResult.success(list);
    }

    // ================================ 小程序码相关接口 ================================

    /**
     * 获取活动小程序码
     */
    @ApiOperation("获取活动小程序码")
    @PreAuthorize("@ss.hasPermi('miniapp:event:qrcode')")
    @PostMapping("/getQRCode")
    public AjaxResult getEventQRCode(@ApiParam("活动ID") @RequestBody Long eventId)
    {
        try
        {
            // 验证活动是否存在
            MiniEvent event = miniEventService.selectMiniEventByEventId(eventId);
            if (event == null)
            {
                return AjaxResult.error("活动不存在");
            }

            // 验证微信配置
            if (!wechatMiniappService.isConfigValid())
            {
                return AjaxResult.error("微信小程序配置不完整，请联系管理员");
            }

            // 生成小程序码
            String base64Image = wechatMiniappService.getEventQRCode(eventId);

            Map<String, Object> result = new HashMap<>();
            result.put("eventId", eventId);
            result.put("eventTitle", event.getTitle());
            result.put("qrcode", "data:image/png;base64," + base64Image);

            return AjaxResult.success("小程序码生成成功", result);
        }
        catch (Exception e)
        {
            logger.error("生成活动小程序码失败，活动ID: {}", eventId, e);
            return AjaxResult.error("生成小程序码失败: " + e.getMessage());
        }
    }

    /**
     * 获取自定义小程序码
     */
    @ApiOperation("获取自定义小程序码")
    @PreAuthorize("@ss.hasPermi('miniapp:event:qrcode')")
    @PostMapping("/getCustomQRCode")
    public AjaxResult getCustomQRCode(@ApiParam("小程序码参数") @RequestBody Map<String, Object> params)
    {
        try
        {
            // 验证必要参数
            String path = (String) params.get("path");
            if (path == null || path.trim().isEmpty())
            {
                return AjaxResult.error("页面路径不能为空");
            }

            // 验证微信配置
            if (!wechatMiniappService.isConfigValid())
            {
                return AjaxResult.error("微信小程序配置不完整，请联系管理员");
            }

            // 解析可选参数
            Integer width = params.get("width") != null ? ((Number) params.get("width")).intValue() : null;
            Boolean autoColor = params.get("autoColor") != null ? (Boolean) params.get("autoColor") : null;
            Map<String, Integer> lineColor = (Map<String, Integer>) params.get("lineColor");
            Boolean isHyaline = params.get("isHyaline") != null ? (Boolean) params.get("isHyaline") : null;
            String envVersion = (String) params.get("envVersion");

            // 生成小程序码
            String base64Image = wechatMiniappService.getQRCode(path, width, autoColor, lineColor, isHyaline, envVersion);

            Map<String, Object> result = new HashMap<>();
            result.put("path", path);
            result.put("qrcode", "data:image/png;base64," + base64Image);

            return AjaxResult.success("小程序码生成成功", result);
        }
        catch (Exception e)
        {
            logger.error("生成自定义小程序码失败", e);
            return AjaxResult.error("生成小程序码失败: " + e.getMessage());
        }
    }
}