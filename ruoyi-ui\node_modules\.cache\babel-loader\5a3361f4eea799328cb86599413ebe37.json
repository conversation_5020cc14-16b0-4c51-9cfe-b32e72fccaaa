{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\config\\homemodule\\index.vue", "mtime": 1754295969458}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}